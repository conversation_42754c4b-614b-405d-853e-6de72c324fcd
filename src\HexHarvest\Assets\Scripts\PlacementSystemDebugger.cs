using UnityEngine;

/// <summary>
/// Placement sistemi için debug ve test araçları
/// </summary>
public class PlacementSystemDebugger : MonoBehaviour
{
    [Header("Debug Settings")]
    [SerializeField] private bool enableDebugMode = true;
    [SerializeField] private bool logAllPlacementEvents = true;
    
    [Header("Test Settings")]
    [SerializeField] private bool testCardSelection = false;
    [SerializeField] private bool testHexClick = false;
    [SerializeField] private bool clearAllSelections = false;
    
    private PlacementController placementController;

    private void Start()
    {
        // PlacementController referansını al
        placementController = FindObjectOfType<PlacementController>();
        
        if (placementController == null)
        {
            Debug.LogWarning("PlacementSystemDebugger: PlacementController bulunamadı!");
        }
        
        if (logAllPlacementEvents)
        {
            // Placement event'lerini dinle
            EventBus.Subscribe<CardSelectedEvent>(OnCardSelected);
            EventBus.Subscribe<CardDeselectedEvent>(OnCardDeselected);
            EventBus.Subscribe<HexClickedEvent>(OnHexClicked);
            EventBus.Subscribe<ObjectPlacedOnHexEvent>(OnObjectPlacedOnHex);
            
            Debug.Log("PlacementSystemDebugger: Event listener'lar kuruldu");
        }
    }
    
    private void OnDestroy()
    {
        if (logAllPlacementEvents)
        {
            // Event listener'larını temizle
            EventBus.Unsubscribe<CardSelectedEvent>(OnCardSelected);
            EventBus.Unsubscribe<CardDeselectedEvent>(OnCardDeselected);
            EventBus.Unsubscribe<HexClickedEvent>(OnHexClicked);
            EventBus.Unsubscribe<ObjectPlacedOnHexEvent>(OnObjectPlacedOnHex);
        }
    }
    
    private void Update()
    {
        // Test butonları
        if (testCardSelection)
        {
            testCardSelection = false;
            TestCardSelection();
        }
        
        if (testHexClick)
        {
            testHexClick = false;
            TestHexClick();
        }
        
        if (clearAllSelections)
        {
            clearAllSelections = false;
            ClearAllSelections();
        }
    }
    
    // Event Handler'lar
    private void OnCardSelected(CardSelectedEvent eventData)
    {
        Debug.Log($"[PlacementDebugger] Card Selected: {eventData.Card.CardName}");
    }
    
    private void OnCardDeselected(CardDeselectedEvent eventData)
    {
        Debug.Log($"[PlacementDebugger] Card Deselected: {eventData.Card.CardName}");
    }
    
    private void OnHexClicked(HexClickedEvent eventData)
    {
        Debug.Log($"[PlacementDebugger] Hex Clicked: State={eventData.Hex.GetStateString()}, Position={eventData.ClickPosition}");
    }
    
    private void OnObjectPlacedOnHex(ObjectPlacedOnHexEvent eventData)
    {
        Debug.Log($"[PlacementDebugger] Object Placed: {eventData.Card.CardName} on hex at {eventData.Hex.transform.position}");
    }
    
    // Test Metodları
    private void TestCardSelection()
    {
        CardUI[] cardUIs = FindObjectsOfType<CardUI>();
        if (cardUIs.Length > 0)
        {
            CardUI testCard = cardUIs[0];
            if (testCard.CurrentCard != null)
            {
                EventBus.Publish(new CardSelectedEvent(testCard.CurrentCard, testCard));
                Debug.Log($"[PlacementDebugger] Test card selection: {testCard.CurrentCard.CardName}");
            }
        }
        else
        {
            Debug.LogWarning("[PlacementDebugger] Test için CardUI bulunamadı!");
        }
    }
    
    private void TestHexClick()
    {
        BaseHex[] hexes = FindObjectsOfType<BaseHex>();
        if (hexes.Length > 0)
        {
            BaseHex testHex = hexes[0];
            EventBus.Publish(new HexClickedEvent(testHex, testHex.transform.position));
            Debug.Log($"[PlacementDebugger] Test hex click: {testHex.GetStateString()}");
        }
        else
        {
            Debug.LogWarning("[PlacementDebugger] Test için BaseHex bulunamadı!");
        }
    }
    
    private void ClearAllSelections()
    {
        if (placementController != null)
        {
            placementController.ClearSelection();
            Debug.Log("[PlacementDebugger] Tüm seçimler temizlendi");
        }
    }
    
    // Inspector Context Menu'ler
    [ContextMenu("Show Placement System Status")]
    private void ShowPlacementSystemStatus()
    {
        Debug.Log("=== PLACEMENT SYSTEM STATUS ===");
        
        if (placementController != null)
        {
            Debug.Log($"PlacementController: {(placementController.HasSelectedCard ? "Kart seçili" : "Kart seçili değil")}");
            if (placementController.HasSelectedCard)
            {
                Debug.Log($"Selected Card: {placementController.SelectedCard.CardName}");
            }
        }
        
        CardUI[] cardUIs = FindObjectsOfType<CardUI>();
        Debug.Log($"Total CardUI count: {cardUIs.Length}");
        
        int selectedCards = 0;
        foreach (CardUI cardUI in cardUIs)
        {
            if (cardUI.IsSelected)
            {
                selectedCards++;
                Debug.Log($"Selected CardUI: {cardUI.name} - {cardUI.CurrentCard?.CardName}");
            }
        }
        Debug.Log($"Selected CardUI count: {selectedCards}");
        
        BaseHex[] hexes = FindObjectsOfType<BaseHex>();
        Debug.Log($"Total BaseHex count: {hexes.Length}");
        
        int occupiedHexes = 0;
        foreach (BaseHex hex in hexes)
        {
            if (hex.HasPlacedObject)
            {
                occupiedHexes++;
                Debug.Log($"Occupied Hex: {hex.name} - State: {hex.GetStateString()}");
            }
        }
        Debug.Log($"Occupied Hex count: {occupiedHexes}");
        
        Debug.Log("=== END STATUS ===");
    }
    
    [ContextMenu("Test Full Placement Flow")]
    private void TestFullPlacementFlow()
    {
        Debug.Log("[PlacementDebugger] Testing full placement flow...");
        
        // 1. Kart seç
        TestCardSelection();
        
        // 2. 1 saniye bekle
        Invoke(nameof(TestHexClick), 1f);
        
        Debug.Log("[PlacementDebugger] Full placement flow test başlatıldı");
    }
}
