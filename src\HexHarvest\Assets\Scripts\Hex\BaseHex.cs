using UnityEngine;

public class BaseHex : MonoBehaviour
{
    [System.Serializable]
    public enum HexState
    {
        Empty,      // Boş durum
        Full,       // Dolu durum  
        Destroyed   // Yıkılmış durum
    }

    [Header("Hex Settings")]
    [SerializeField] private HexState currentState = HexState.Empty;
    
    [Header("Y Position Values")]
    [SerializeField] private float emptyYPosition = -0.73f;
    [SerializeField] private float fullYPosition = -0.462f;
    [SerializeField] private float destroyedYPosition = -1.556f;
    
    [Header("Animation Settings")]
    [SerializeField] private float transitionSpeed = 2f;
    [SerializeField] private bool useAnimation = true;

    [Header("Interaction")]
    [SerializeField] private bool isClickable = true;

    private Vector3 targetPosition;
    private bool isTransitioning = false;
    private GameObject placedObject;

    // Properties
    public HexState CurrentState 
    { 
        get { return currentState; } 
        private set { currentState = value; }
    }

    public bool IsTransitioning
    {
        get { return isTransitioning; }
    }

    public bool IsClickable
    {
        get { return isClickable; }
        set { isClickable = value; }
    }

    public GameObject PlacedObject
    {
        get { return placedObject; }
    }

    public bool HasPlacedObject
    {
        get { return placedObject != null; }
    }

    private void Start()
    {
        // Başlangıçta mevcut state'e göre pozisyonu ayarla
        SetPositionForState(currentState, false);

        // Collider kontrolü
        if (GetComponent<Collider2D>() == null && GetComponent<Collider>() == null)
        {
            Debug.LogWarning($"BaseHex ({gameObject.name}): OnMouseDown çalışması için Collider2D veya Collider gerekli!");
        }
    }

    private void Update()
    {
        // Animasyon aktifse pozisyon geçişini yap
        if (isTransitioning && useAnimation)
        {
            transform.position = Vector3.MoveTowards(transform.position, targetPosition, transitionSpeed * Time.deltaTime);
            
            if (Vector3.Distance(transform.position, targetPosition) < 0.01f)
            {
                transform.position = targetPosition;
                isTransitioning = false;
            }
        }
    }

    /// <summary>
    /// Hex'in state'ini değiştirir
    /// </summary>
    /// <param name="newState">Yeni state</param>
    /// <param name="animated">Animasyonlu geçiş yapılsın mı</param>
    public void ChangeState(HexState newState, bool animated = true)
    {
        if (currentState == newState) return;

        HexState previousState = currentState;
        currentState = newState;
        SetPositionForState(newState, animated && useAnimation);

        // EventBus ile hex state değişikliğini bildir
        EventBus.Publish(new HexStateChangedEvent(this, newState, previousState));
    }

    /// <summary>
    /// Hex'i boş duruma getirir
    /// </summary>
    /// <param name="animated">Animasyonlu geçiş yapılsın mı</param>
    public void SetEmpty(bool animated = true)
    {
        ChangeState(HexState.Empty, animated);
    }

    /// <summary>
    /// Hex'i dolu duruma getirir
    /// </summary>
    /// <param name="animated">Animasyonlu geçiş yapılsın mı</param>
    public void SetFull(bool animated = true)
    {
        ChangeState(HexState.Full, animated);
    }

    /// <summary>
    /// Hex'i yıkılmış duruma getirir
    /// </summary>
    /// <param name="animated">Animasyonlu geçiş yapılsın mı</param>
    public void SetDestroyed(bool animated = true)
    {
        ChangeState(HexState.Destroyed, animated);
    }

    /// <summary>
    /// Belirtilen state için pozisyonu ayarlar
    /// </summary>
    /// <param name="state">Hedef state</param>
    /// <param name="animated">Animasyonlu geçiş yapılsın mı</param>
    private void SetPositionForState(HexState state, bool animated)
    {
        float targetY = GetYPositionForState(state);
        targetPosition = new Vector3(transform.position.x, targetY, transform.position.z);

        if (animated && useAnimation)
        {
            isTransitioning = true;
        }
        else
        {
            transform.position = targetPosition;
            isTransitioning = false;
        }
    }

    /// <summary>
    /// Belirtilen state için Y pozisyonunu döndürür
    /// </summary>
    /// <param name="state">State</param>
    /// <returns>Y pozisyonu</returns>
    private float GetYPositionForState(HexState state)
    {
        switch (state)
        {
            case HexState.Empty:
                return emptyYPosition;
            case HexState.Full:
                return fullYPosition;
            case HexState.Destroyed:
                return destroyedYPosition;
            default:
                return emptyYPosition;
        }
    }

    /// <summary>
    /// Mevcut state'in string temsilini döndürür
    /// </summary>
    /// <returns>State adı</returns>
    public string GetStateString()
    {
        switch (currentState)
        {
            case HexState.Empty:
                return "Boş";
            case HexState.Full:
                return "Dolu";
            case HexState.Destroyed:
                return "Yıkılmış";
            default:
                return "Bilinmeyen";
        }
    }

    /// <summary>
    /// Inspector'da state değişikliği için test metodları
    /// </summary>
    [ContextMenu("Test - Set Empty")]
    private void TestSetEmpty()
    {
        SetEmpty();
    }

    [ContextMenu("Test - Set Full")]
    private void TestSetFull()
    {
        SetFull();
    }

    [ContextMenu("Test - Set Destroyed")]
    private void TestSetDestroyed()
    {
        SetDestroyed();
    }

    // Editor'da state değişikliklerini görmek için
    private void OnValidate()
    {
        if (Application.isPlaying) return;
        
        // Editor'da state değiştirildiğinde pozisyonu güncelle
        SetPositionForState(currentState, false);
    }

    /// <summary>
    /// Mouse click detection
    /// </summary>
    private void OnMouseDown()
    {
        if (isClickable)
        {
            Vector3 clickPosition = Camera.main.ScreenToWorldPoint(Input.mousePosition);
            clickPosition.z = 0f; // 2D için z'yi sıfırla

            // EventBus ile hex click event'ini gönder
            EventBus.Publish(new HexClickedEvent(this, clickPosition));

            Debug.Log($"BaseHex: Hex tıklandı! State: {GetStateString()}, Position: {transform.position}");
        }
    }

    /// <summary>
    /// Hex'e obje yerleştirir
    /// </summary>
    /// <param name="card">Yerleştirilecek kartın verisi</param>
    /// <returns>Yerleştirilen obje</returns>
    public GameObject PlaceObject(CardSO card)
    {
        if (card == null || card.CardPrefab == null)
        {
            Debug.LogWarning("BaseHex: Geçersiz kart veya prefab!");
            return null;
        }

        // Eğer zaten bir obje varsa, onu yok et
        if (placedObject != null)
        {
            DestroyImmediate(placedObject);
        }

        // Yeni objeyi oluştur
        placedObject = Instantiate(card.CardPrefab, transform);

        // Objeyi hex'in üzerine yerleştir
        placedObject.transform.localPosition = Vector3.zero;

        // Hex'i dolu duruma getir
        ChangeState(HexState.Full);

        // EventBus ile obje yerleştirme event'ini gönder
        EventBus.Publish(new ObjectPlacedOnHexEvent(this, card, placedObject));

        Debug.Log($"BaseHex: {card.CardName} objesi hex'e yerleştirildi!");

        return placedObject;
    }

    /// <summary>
    /// Hex'teki objeyi kaldırır
    /// </summary>
    public void RemoveObject()
    {
        if (placedObject != null)
        {
            DestroyImmediate(placedObject);
            placedObject = null;

            // Hex'i boş duruma getir
            ChangeState(HexState.Empty);

            Debug.Log("BaseHex: Obje hex'ten kaldırıldı!");
        }
    }
}
