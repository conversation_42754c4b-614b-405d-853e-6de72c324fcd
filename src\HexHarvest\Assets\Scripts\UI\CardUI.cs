using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class CardUI : MonoBehaviour
{
    [Header("UI References")]
    [SerializeField] private Image cardIcon;
    [SerializeField] private TextMeshProUGUI cardNameText;
    [SerializeField] private But<PERSON> cardButton;
    
    [Header("Card Data")]
    [SerializeField] private CardSO currentCard;

    [Header("Selection Settings")]
    [SerializeField] private float selectedScale = 1.2f;
    [SerializeField] private float scaleAnimationSpeed = 5f;
    [SerializeField] private Color selectedColor = Color.yellow;
    [SerializeField] private Color normalColor = Color.white;

    // Private variables
    private bool isSelected = false;
    private Vector3 originalScale;
    private Image backgroundImage;

    // Properties
    public CardSO CurrentCard
    {
        get { return currentCard; }
        private set { currentCard = value; }
    }

    public bool IsActive
    {
        get { return gameObject.activeSelf; }
    }

    public bool IsSelected
    {
        get { return isSelected; }
    }

    private void Start()
    {
        // Button click event'ini bağla
        if (cardButton == null)
        {
            cardButton = GetComponent<Button>();
        }

        if (cardButton != null)
        {
            cardButton.onClick.AddListener(OnCardClicked);
        }

        // Background image referansını al
        backgroundImage = GetComponent<Image>();

        // Orijinal scale'i kaydet
        originalScale = transform.localScale;

        // EventBus event'lerini dinle (şimdilik yok)
    }

    private void OnDestroy()
    {
        // Button event'ini temizle
        if (cardButton != null)
        {
            cardButton.onClick.RemoveListener(OnCardClicked);
        }

        // EventBus listener'ını temizle (şimdilik yok)
    }

    /// <summary>
    /// Kart tıklandığında çağrılır
    /// </summary>
    private void OnCardClicked()
    {
        if (currentCard != null)
        {
            if (!isSelected)
            {
                // Kartı seç
                SelectCard();
            }
            else
            {
                // Kartın seçimini kaldır
                DeselectCard();
            }

            // EventBus ile card click event'ini gönder
            Vector3 clickPosition = transform.position;
            EventBus.Publish(new CardClickedEvent(currentCard, clickPosition));

            Debug.Log($"CardUI: {currentCard.CardName} kartı tıklandı! Seçili: {isSelected}");
        }
    }



    /// <summary>
    /// Kartı seçer
    /// </summary>
    private void SelectCard()
    {
        if (isSelected) return;

        isSelected = true;

        // Görsel değişiklikleri uygula
        StartCoroutine(AnimateScale(originalScale * selectedScale));
        if (backgroundImage != null)
        {
            backgroundImage.color = selectedColor;
        }

        // EventBus ile seçilme event'ini gönder
        EventBus.Publish(new CardSelectedEvent(currentCard, this));

        Debug.Log($"CardUI: {currentCard.CardName} kartı seçildi! Event gönderildi.");
    }

    /// <summary>
    /// Kartın seçimini kaldırır
    /// </summary>
    public void DeselectCard()
    {
        if (!isSelected) return;

        isSelected = false;

        // Görsel değişiklikleri geri al
        StartCoroutine(AnimateScale(originalScale));
        if (backgroundImage != null)
        {
            backgroundImage.color = normalColor;
        }

        Debug.Log($"CardUI: {currentCard.CardName} kartının seçimi kaldırıldı!");
    }

    /// <summary>
    /// Kartın interactable durumunu ayarlar
    /// </summary>
    /// <param name="interactable">Interactable olsun mu</param>
    public void SetInteractable(bool interactable)
    {
        if (cardButton != null)
        {
            cardButton.interactable = interactable;
        }

        // Görsel feedback
        if (backgroundImage != null)
        {
            Color currentColor = backgroundImage.color;
            currentColor.a = interactable ? 1f : 0.5f;
            backgroundImage.color = currentColor;
        }
    }

    /// <summary>
    /// Scale animasyonu coroutine'i
    /// </summary>
    /// <param name="targetScale">Hedef scale</param>
    /// <returns></returns>
    private System.Collections.IEnumerator AnimateScale(Vector3 targetScale)
    {
        Vector3 startScale = transform.localScale;
        float elapsedTime = 0f;
        float duration = 1f / scaleAnimationSpeed;

        while (elapsedTime < duration)
        {
            elapsedTime += Time.deltaTime;
            float t = elapsedTime / duration;

            // Smooth animation curve
            t = Mathf.SmoothStep(0f, 1f, t);

            transform.localScale = Vector3.Lerp(startScale, targetScale, t);
            yield return null;
        }

        transform.localScale = targetScale;
    }

    /// <summary>
    /// Kartı ayarlar ve UI'ı günceller
    /// </summary>
    /// <param name="cardData">Kart verisi</param>
    public void SetCard(CardSO cardData)
    {
        if (cardData == null)
        {
            Debug.LogWarning("CardUI: Null card data!");
            return;
        }

        currentCard = cardData;
        UpdateUI();
    }

    /// <summary>
    /// UI'ı günceller
    /// </summary>
    private void UpdateUI()
    {
        if (currentCard == null) return;

        // Kart ikonu
        if (cardIcon != null && currentCard.CardSprite != null)
        {
            cardIcon.sprite = currentCard.CardSprite;
            cardIcon.color = Color.white;
        }

        // Kart ismi
        if (cardNameText != null)
        {
            cardNameText.text = currentCard.CardName;
        }
    }

    /// <summary>
    /// Kartı temizler
    /// </summary>
    public void ClearCard()
    {
        currentCard = null;

        if (cardIcon != null)
        {
            cardIcon.sprite = null;
            cardIcon.color = Color.clear;
        }

        if (cardNameText != null)
        {
            cardNameText.text = "";
        }
    }

    /// <summary>
    /// Kartı aktif/pasif yapar
    /// </summary>
    /// <param name="active">Aktif mi</param>
    public void SetActive(bool active)
    {
        gameObject.SetActive(active);
    }



    /// <summary>
    /// Kartın mevcut mevsimle uyumlu olup olmadığını kontrol eder
    /// </summary>
    /// <param name="currentSeason">Mevcut mevsim</param>
    /// <returns>Uyumlu mu</returns>
    public bool IsCompatibleWithSeason(Season currentSeason)
    {
        return currentCard != null && currentCard.CardSeason == currentSeason;
    }

    /// <summary>
    /// Kartı pool'a döndürmeye hazırlar
    /// </summary>
    public void PrepareForPool()
    {
        ClearCard();
        SetActive(false);
        // Parent'ı null yapmak yerine, CardPool.ReturnCard() metodunda parent ayarlanacak
    }

    /// <summary>
    /// Kartı pool'dan çıkarıp kullanıma hazırlar
    /// </summary>
    /// <param name="parent">Yeni parent transform</param>
    public void PrepareForUse(Transform parent = null)
    {
        if (parent != null)
        {
            transform.SetParent(parent);
        }
        
        SetActive(true);
    }

    // Debug için
    private void OnValidate()
    {
        if (Application.isPlaying && currentCard != null)
        {
            UpdateUI();
        }
    }
}
