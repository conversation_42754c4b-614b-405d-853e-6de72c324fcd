using UnityEngine;

/// <summary>
/// Kart seçimi ve hex'e obje yerleştirme işlemlerini yöneten controller
/// </summary>
public class PlacementController : MonoBehaviour
{
    [Header("Debug")]
    [SerializeField] private bool showDebugInfo = true;
    
    // Private variables
    private CardSO selectedCard = null;
    private CardUI selectedCardUI = null;
    
    // Properties
    public bool HasSelectedCard
    {
        get { return selectedCard != null; }
    }
    
    public CardSO SelectedCard
    {
        get { return selectedCard; }
    }

    private void Start()
    {
        // EventBus event'lerini dinle
        EventBus.Subscribe<CardSelectedEvent>(OnCardSelected);
        EventBus.Subscribe<CardDeselectedEvent>(OnCardDeselected);
        EventBus.Subscribe<HexClickedEvent>(OnHexClicked);
        EventBus.Subscribe<ObjectPlacedOnHexEvent>(OnObjectPlacedOnHex);

        Debug.Log("PlacementController: Event listener'lar kuruldu ve aktif!");

        if (showDebugInfo)
        {
            Debug.Log("PlacementController: Debug modu aktif");
        }
    }

    private void OnDestroy()
    {
        // EventBus listener'larını temizle
        EventBus.Unsubscribe<CardSelectedEvent>(OnCardSelected);
        EventBus.Unsubscribe<CardDeselectedEvent>(OnCardDeselected);
        EventBus.Unsubscribe<HexClickedEvent>(OnHexClicked);
        EventBus.Unsubscribe<ObjectPlacedOnHexEvent>(OnObjectPlacedOnHex);
    }

    /// <summary>
    /// Kart seçildiğinde çağrılır
    /// </summary>
    /// <param name="eventData">Event verisi</param>
    private void OnCardSelected(CardSelectedEvent eventData)
    {
        selectedCard = eventData.Card;
        selectedCardUI = eventData.CardUI;

        Debug.Log($"PlacementController: Kart seçildi - {selectedCard.CardName}");
    }

    /// <summary>
    /// Kart seçimi kaldırıldığında çağrılır
    /// </summary>
    /// <param name="eventData">Event verisi</param>
    private void OnCardDeselected(CardDeselectedEvent eventData)
    {
        // Eğer seçili kart bu kartsa, seçimi temizle
        if (selectedCard == eventData.Card)
        {
            selectedCard = null;
            selectedCardUI = null;
            
            if (showDebugInfo)
            {
                Debug.Log($"PlacementController: Kart seçimi kaldırıldı - {eventData.Card.CardName}");
            }
        }
    }

    /// <summary>
    /// Hex tıklandığında çağrılır
    /// </summary>
    /// <param name="eventData">Event verisi</param>
    private void OnHexClicked(HexClickedEvent eventData)
    {
        if (showDebugInfo)
        {
            Debug.Log($"PlacementController: Hex tıklandı - State: {eventData.Hex.GetStateString()}");
        }

        // Eğer seçili kart varsa ve hex boşsa, objeyi yerleştir
        if (HasSelectedCard && CanPlaceOnHex(eventData.Hex))
        {
            PlaceObjectOnHex(eventData.Hex);
        }
        else if (HasSelectedCard && !CanPlaceOnHex(eventData.Hex))
        {
            if (showDebugInfo)
            {
                Debug.Log($"PlacementController: Bu hex'e obje yerleştirilemez - State: {eventData.Hex.GetStateString()}");
            }
        }
        else if (!HasSelectedCard)
        {
            if (showDebugInfo)
            {
                Debug.Log("PlacementController: Önce bir kart seçin!");
            }
        }
    }

    /// <summary>
    /// Obje hex'e yerleştirildiğinde çağrılır
    /// </summary>
    /// <param name="eventData">Event verisi</param>
    private void OnObjectPlacedOnHex(ObjectPlacedOnHexEvent eventData)
    {
        if (showDebugInfo)
        {
            Debug.Log($"PlacementController: {eventData.Card.CardName} objesi hex'e yerleştirildi!");
        }
        
        // Kartı kullanıldı olarak işaretle ve seçimi kaldır
        if (selectedCardUI != null)
        {
            selectedCardUI.SetInteractable(false); // Kartı kullanılamaz yap
        }
        
        // Seçimi temizle
        selectedCard = null;
        selectedCardUI = null;
    }

    /// <summary>
    /// Hex'e obje yerleştirilebilir mi kontrol eder
    /// </summary>
    /// <param name="hex">Kontrol edilecek hex</param>
    /// <returns>Yerleştirilebilirse true</returns>
    private bool CanPlaceOnHex(BaseHex hex)
    {
        if (hex == null) return false;
        
        // Sadece boş hex'lere yerleştirilebilir
        return hex.CurrentState == BaseHex.HexState.Empty && !hex.HasPlacedObject;
    }

    /// <summary>
    /// Seçili kartın objesini hex'e yerleştirir
    /// </summary>
    /// <param name="hex">Hedef hex</param>
    private void PlaceObjectOnHex(BaseHex hex)
    {
        if (selectedCard == null || hex == null)
        {
            Debug.LogWarning("PlacementController: Geçersiz kart veya hex!");
            return;
        }

        // Hex'e objeyi yerleştir
        GameObject placedObject = hex.PlaceObject(selectedCard);
        
        if (placedObject != null && showDebugInfo)
        {
            Debug.Log($"PlacementController: {selectedCard.CardName} başarıyla yerleştirildi!");
        }
    }

    /// <summary>
    /// Mevcut seçimi temizler
    /// </summary>
    public void ClearSelection()
    {
        if (selectedCardUI != null)
        {
            selectedCardUI.DeselectCard();
        }
        
        selectedCard = null;
        selectedCardUI = null;
        
        if (showDebugInfo)
        {
            Debug.Log("PlacementController: Seçim temizlendi");
        }
    }

    /// <summary>
    /// Debug bilgilerini gösterir
    /// </summary>
    [ContextMenu("Show Debug Info")]
    private void ShowDebugInfo()
    {
        Debug.Log($"PlacementController Debug Info:");
        Debug.Log($"- Has Selected Card: {HasSelectedCard}");
        Debug.Log($"- Selected Card: {(selectedCard != null ? selectedCard.CardName : "None")}");
        Debug.Log($"- Selected Card UI: {(selectedCardUI != null ? selectedCardUI.name : "None")}");
    }
}
