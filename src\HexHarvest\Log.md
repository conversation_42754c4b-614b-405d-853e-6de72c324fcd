PlacementController: Instance oluşturuldu
UnityEngine.Debug:Log (object)
PlacementController:Awake () (at Assets/Scripts/Controller/PlacementController.cs:34)
UnityEngine.GameObject:AddComponent<PlacementController> ()
GameManager:EnsurePlacementController () (at Assets/Scripts/Manager/GameManager.cs:178)
GameManager:Awake () (at Assets/Scripts/Manager/GameManager.cs:70)

GameManager: PlacementController otomatik olarak oluşturuldu
UnityEngine.Debug:Log (object)
GameManager:EnsurePlacementController () (at Assets/Scripts/Manager/GameManager.cs:179)
GameManager:Awake () (at Assets/Scripts/Manager/GameManager.cs:70)

PlacementController: Event listener'lar kuruldu ve aktif!
UnityEngine.Debug:Log (object)
PlacementController:Start () (at Assets/Scripts/Controller/PlacementController.cs:51)

PlacementController: Debug modu aktif
UnityEngine.Debug:Log (object)
PlacementController:Start () (at Assets/Scripts/Controller/PlacementController.cs:55)

CardUI: CardSelectedEvent gönderiliyor - currentCard null mu? False
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:121)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Kart adı: Corn 
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:124)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: OnCardSelected çağrıldı
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:74)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: eventData.Card null mu? False
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:75)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: eventData.CardUI null mu? False
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:76)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: Kart seçildi - Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:89)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Corn  kartı seçildi! Event gönderildi.
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:129)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Corn  kartı tıklandı! Seçili: True
UnityEngine.Debug:Log (object)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:98)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: Hex tıklandı - State: Boş
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:127)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Obje yerleştiriliyor...
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:133)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: PlaceObjectOnHex çağrıldı
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:199)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: selectedCard null mu? False
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:200)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: hex null mu? False
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:201)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Kart adı: Corn 
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:221)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Corn  objesi hex'e yerleştirildi!
UnityEngine.Debug:Log (object)
PlacementController:OnObjectPlacedOnHex (ObjectPlacedOnHexEvent) (at Assets/Scripts/Controller/PlacementController.cs:154)
EventBus:Publish<ObjectPlacedOnHexEvent> (ObjectPlacedOnHexEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:284)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:224)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Corn  kartı pool'a döndürüldü
UnityEngine.Debug:Log (object)
PlacementController:OnObjectPlacedOnHex (ObjectPlacedOnHexEvent) (at Assets/Scripts/Controller/PlacementController.cs:165)
EventBus:Publish<ObjectPlacedOnHexEvent> (ObjectPlacedOnHexEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:284)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:224)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Corn  objesi hex'e yerleştirildi!
UnityEngine.Debug:Log (object)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:286)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:224)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: NULL CARD başarıyla yerleştirildi!
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:229)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu, Position: (-3.62, -0.73, 0.00), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Boş
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: False
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Önce bir kart seçin!
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:142)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Boş, Position: (-3.62, -0.73, 0.00), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

Mevsim geçişi başladı - Geri sayım başlatıldı
UnityEngine.Debug:Log (object)
UIManager:OnSeasonTransitionStateChanged (SeasonTransitionStateChangedEvent) (at Assets/Scripts/Manager/UIManager.cs:125)
EventBus:Publish<SeasonTransitionStateChangedEvent> (SeasonTransitionStateChangedEvent) (at Assets/Scripts/EventBus.cs:284)
GameManager:set_IsTransitioning (bool) (at Assets/Scripts/Manager/GameManager.cs:46)
GameManager/<SeasonTransitionCoroutine>d__21:MoveNext () (at Assets/Scripts/Manager/GameManager.cs:127)
UnityEngine.MonoBehaviour:StartCoroutine (System.Collections.IEnumerator)
GameManager:NextSeason () (at Assets/Scripts/Manager/GameManager.cs:113)
GameManager:OnReadyButtonClicked (ReadyButtonClickedEvent) (at Assets/Scripts/Manager/GameManager.cs:103)
EventBus:Publish<ReadyButtonClickedEvent> (ReadyButtonClickedEvent) (at Assets/Scripts/EventBus.cs:284)
UIManager:OnReadyButtonClicked () (at Assets/Scripts/Manager/UIManager.cs:83)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

Mevsim geçişi başladı: İlkbahar → Yaz
UnityEngine.Debug:Log (object)
GameManager/<SeasonTransitionCoroutine>d__21:MoveNext () (at Assets/Scripts/Manager/GameManager.cs:130)
UnityEngine.MonoBehaviour:StartCoroutine (System.Collections.IEnumerator)
GameManager:NextSeason () (at Assets/Scripts/Manager/GameManager.cs:113)
GameManager:OnReadyButtonClicked (ReadyButtonClickedEvent) (at Assets/Scripts/Manager/GameManager.cs:103)
EventBus:Publish<ReadyButtonClickedEvent> (ReadyButtonClickedEvent) (at Assets/Scripts/EventBus.cs:284)
UIManager:OnReadyButtonClicked () (at Assets/Scripts/Manager/UIManager.cs:83)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardPool: Bu kart zaten pool'da veya bu pool'a ait değil!
UnityEngine.Debug:LogWarning (object)
CardPool:ReturnCard (CardUI) (at Assets/Scripts/Pool/CardPool.cs:139)
CardController:ReturnAllDisplayedCards () (at Assets/Scripts/Controller/CardController.cs:212)
CardController:RefreshDisplayedCards () (at Assets/Scripts/Controller/CardController.cs:165)
CardController:OnSeasonChanged (SeasonChangedEvent) (at Assets/Scripts/Controller/CardController.cs:103)
EventBus:Publish<SeasonChangedEvent> (SeasonChangedEvent) (at Assets/Scripts/EventBus.cs:284)
GameManager:set_CurrentSeason (Season) (at Assets/Scripts/Manager/GameManager.cs:31)
GameManager/<SeasonTransitionCoroutine>d__21:MoveNext () (at Assets/Scripts/Manager/GameManager.cs:136)
UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr)

Mevsim geçişi tamamlandı - Ready butonu aktif
UnityEngine.Debug:Log (object)
UIManager:OnSeasonTransitionStateChanged (SeasonTransitionStateChangedEvent) (at Assets/Scripts/Manager/UIManager.cs:136)
EventBus:Publish<SeasonTransitionStateChangedEvent> (SeasonTransitionStateChangedEvent) (at Assets/Scripts/EventBus.cs:284)
GameManager:set_IsTransitioning (bool) (at Assets/Scripts/Manager/GameManager.cs:46)
GameManager/<SeasonTransitionCoroutine>d__21:MoveNext () (at Assets/Scripts/Manager/GameManager.cs:137)
UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr)

Mevsim geçişi tamamlandı: Yaz
UnityEngine.Debug:Log (object)
GameManager/<SeasonTransitionCoroutine>d__21:MoveNext () (at Assets/Scripts/Manager/GameManager.cs:139)
UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr)

CardUI: CardSelectedEvent gönderiliyor - currentCard null mu? False
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:121)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Kart adı: Corn 
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:124)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: OnCardSelected çağrıldı
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:74)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: eventData.Card null mu? False
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:75)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: eventData.CardUI null mu? False
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:76)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: Kart seçildi - Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:89)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Corn  kartı seçildi! Event gönderildi.
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:129)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Corn  kartı tıklandı! Seçili: True
UnityEngine.Debug:Log (object)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:98)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: Hex tıklandı - State: Boş
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:127)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Obje yerleştiriliyor...
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:133)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: PlaceObjectOnHex çağrıldı
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:199)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: selectedCard null mu? False
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:200)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: hex null mu? False
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:201)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Kart adı: Corn 
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:221)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Corn  objesi hex'e yerleştirildi!
UnityEngine.Debug:Log (object)
PlacementController:OnObjectPlacedOnHex (ObjectPlacedOnHexEvent) (at Assets/Scripts/Controller/PlacementController.cs:154)
EventBus:Publish<ObjectPlacedOnHexEvent> (ObjectPlacedOnHexEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:284)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:224)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Corn  kartı pool'a döndürüldü
UnityEngine.Debug:Log (object)
PlacementController:OnObjectPlacedOnHex (ObjectPlacedOnHexEvent) (at Assets/Scripts/Controller/PlacementController.cs:165)
EventBus:Publish<ObjectPlacedOnHexEvent> (ObjectPlacedOnHexEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:284)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:224)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Corn  objesi hex'e yerleştirildi!
UnityEngine.Debug:Log (object)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:286)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:224)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: NULL CARD başarıyla yerleştirildi!
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:229)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu, Position: (-1.81, -0.73, -1.04), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Boş
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: False
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Önce bir kart seçin!
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:142)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Boş, Position: (-1.81, -0.73, -1.04), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

CardUI: CardSelectedEvent gönderiliyor - currentCard null mu? False
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:121)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Kart adı: Corn 
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:124)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: OnCardSelected çağrıldı
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:74)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: eventData.Card null mu? False
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:75)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: eventData.CardUI null mu? False
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:76)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: Kart seçildi - Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:89)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Corn  kartı seçildi! Event gönderildi.
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:129)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Corn  kartı tıklandı! Seçili: True
UnityEngine.Debug:Log (object)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:98)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: Hex tıklandı - State: Boş
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:127)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Obje yerleştiriliyor...
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:133)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: PlaceObjectOnHex çağrıldı
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:199)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: selectedCard null mu? False
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:200)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: hex null mu? False
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:201)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Kart adı: Corn 
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:221)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Corn  objesi hex'e yerleştirildi!
UnityEngine.Debug:Log (object)
PlacementController:OnObjectPlacedOnHex (ObjectPlacedOnHexEvent) (at Assets/Scripts/Controller/PlacementController.cs:154)
EventBus:Publish<ObjectPlacedOnHexEvent> (ObjectPlacedOnHexEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:284)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:224)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Corn  kartı pool'a döndürüldü
UnityEngine.Debug:Log (object)
PlacementController:OnObjectPlacedOnHex (ObjectPlacedOnHexEvent) (at Assets/Scripts/Controller/PlacementController.cs:165)
EventBus:Publish<ObjectPlacedOnHexEvent> (ObjectPlacedOnHexEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:284)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:224)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Corn  objesi hex'e yerleştirildi!
UnityEngine.Debug:Log (object)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:286)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:224)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: NULL CARD başarıyla yerleştirildi!
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:229)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu, Position: (-1.81, -0.73, 1.04), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Boş
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: False
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Önce bir kart seçin!
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:142)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Boş, Position: (-1.81, -0.73, 1.04), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

CardUI: CardSelectedEvent gönderiliyor - currentCard null mu? False
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:121)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Kart adı: Corn 
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:124)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: OnCardSelected çağrıldı
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:74)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: eventData.Card null mu? False
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:75)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: eventData.CardUI null mu? False
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:76)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: Kart seçildi - Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:89)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Corn  kartı seçildi! Event gönderildi.
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:129)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Corn  kartı tıklandı! Seçili: True
UnityEngine.Debug:Log (object)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:98)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: Hex tıklandı - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:127)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Bu hex'e obje yerleştirilemez - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:138)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu, Position: (-1.81, -0.46, -1.04), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Boş
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:127)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Obje yerleştiriliyor...
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:133)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: PlaceObjectOnHex çağrıldı
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:199)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: selectedCard null mu? False
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:200)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: hex null mu? False
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:201)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Kart adı: Corn 
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:221)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Corn  objesi hex'e yerleştirildi!
UnityEngine.Debug:Log (object)
PlacementController:OnObjectPlacedOnHex (ObjectPlacedOnHexEvent) (at Assets/Scripts/Controller/PlacementController.cs:154)
EventBus:Publish<ObjectPlacedOnHexEvent> (ObjectPlacedOnHexEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:284)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:224)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Corn  kartı pool'a döndürüldü
UnityEngine.Debug:Log (object)
PlacementController:OnObjectPlacedOnHex (ObjectPlacedOnHexEvent) (at Assets/Scripts/Controller/PlacementController.cs:165)
EventBus:Publish<ObjectPlacedOnHexEvent> (ObjectPlacedOnHexEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:284)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:224)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Corn  objesi hex'e yerleştirildi!
UnityEngine.Debug:Log (object)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:286)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:224)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: NULL CARD başarıyla yerleştirildi!
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:229)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu, Position: (-1.81, -0.46, -1.04), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

CardUI: CardSelectedEvent gönderiliyor - currentCard null mu? False
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:121)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Kart adı: Corn 
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:124)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: OnCardSelected çağrıldı
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:74)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: eventData.Card null mu? False
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:75)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: eventData.CardUI null mu? False
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:76)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: Kart seçildi - Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:89)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Corn  kartı seçildi! Event gönderildi.
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:129)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Corn  kartı tıklandı! Seçili: True
UnityEngine.Debug:Log (object)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:98)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: Hex tıklandı - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:127)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Bu hex'e obje yerleştirilemez - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:138)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu, Position: (-1.81, -0.46, -1.04), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:127)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Bu hex'e obje yerleştirilemez - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:138)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu, Position: (-1.81, -0.46, -1.04), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:127)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Bu hex'e obje yerleştirilemez - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:138)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu, Position: (-1.81, -0.46, -1.04), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:127)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Bu hex'e obje yerleştirilemez - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:138)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu, Position: (-1.81, -0.46, -1.04), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:127)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Bu hex'e obje yerleştirilemez - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:138)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu, Position: (-1.81, -0.46, -1.04), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:127)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Bu hex'e obje yerleştirilemez - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:138)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu, Position: (-1.81, -0.46, -1.04), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:127)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Bu hex'e obje yerleştirilemez - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:138)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu, Position: (-3.62, -0.46, 0.00), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Boş
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:127)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Obje yerleştiriliyor...
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:133)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: PlaceObjectOnHex çağrıldı
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:199)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: selectedCard null mu? False
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:200)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: hex null mu? False
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:201)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Kart adı: Corn 
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:221)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Corn  objesi hex'e yerleştirildi!
UnityEngine.Debug:Log (object)
PlacementController:OnObjectPlacedOnHex (ObjectPlacedOnHexEvent) (at Assets/Scripts/Controller/PlacementController.cs:154)
EventBus:Publish<ObjectPlacedOnHexEvent> (ObjectPlacedOnHexEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:284)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:224)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Corn  kartı pool'a döndürüldü
UnityEngine.Debug:Log (object)
PlacementController:OnObjectPlacedOnHex (ObjectPlacedOnHexEvent) (at Assets/Scripts/Controller/PlacementController.cs:165)
EventBus:Publish<ObjectPlacedOnHexEvent> (ObjectPlacedOnHexEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:284)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:224)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Corn  objesi hex'e yerleştirildi!
UnityEngine.Debug:Log (object)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:286)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:224)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: NULL CARD başarıyla yerleştirildi!
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:229)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu, Position: (-3.62, -0.46, 0.00), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

CardUI: CardSelectedEvent gönderiliyor - currentCard null mu? False
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:121)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Kart adı: Corn 
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:124)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: OnCardSelected çağrıldı
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:74)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: eventData.Card null mu? False
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:75)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: eventData.CardUI null mu? False
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:76)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: Kart seçildi - Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:89)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Corn  kartı seçildi! Event gönderildi.
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:129)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Corn  kartı tıklandı! Seçili: True
UnityEngine.Debug:Log (object)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:98)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: Hex tıklandı - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:127)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Bu hex'e obje yerleştirilemez - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:138)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu, Position: (-1.81, -0.46, 1.04), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Boş
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:127)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Obje yerleştiriliyor...
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:133)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: PlaceObjectOnHex çağrıldı
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:199)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: selectedCard null mu? False
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:200)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: hex null mu? False
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:201)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Kart adı: Corn 
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:221)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Corn  objesi hex'e yerleştirildi!
UnityEngine.Debug:Log (object)
PlacementController:OnObjectPlacedOnHex (ObjectPlacedOnHexEvent) (at Assets/Scripts/Controller/PlacementController.cs:154)
EventBus:Publish<ObjectPlacedOnHexEvent> (ObjectPlacedOnHexEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:284)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:224)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Corn  kartı pool'a döndürüldü
UnityEngine.Debug:Log (object)
PlacementController:OnObjectPlacedOnHex (ObjectPlacedOnHexEvent) (at Assets/Scripts/Controller/PlacementController.cs:165)
EventBus:Publish<ObjectPlacedOnHexEvent> (ObjectPlacedOnHexEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:284)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:224)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Corn  objesi hex'e yerleştirildi!
UnityEngine.Debug:Log (object)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:286)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:224)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: NULL CARD başarıyla yerleştirildi!
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:229)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu, Position: (-1.81, -0.46, 1.04), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

Mevsim geçişi başladı - Geri sayım başlatıldı
UnityEngine.Debug:Log (object)
UIManager:OnSeasonTransitionStateChanged (SeasonTransitionStateChangedEvent) (at Assets/Scripts/Manager/UIManager.cs:125)
EventBus:Publish<SeasonTransitionStateChangedEvent> (SeasonTransitionStateChangedEvent) (at Assets/Scripts/EventBus.cs:284)
GameManager:set_IsTransitioning (bool) (at Assets/Scripts/Manager/GameManager.cs:46)
GameManager/<SeasonTransitionCoroutine>d__21:MoveNext () (at Assets/Scripts/Manager/GameManager.cs:127)
UnityEngine.MonoBehaviour:StartCoroutine (System.Collections.IEnumerator)
GameManager:NextSeason () (at Assets/Scripts/Manager/GameManager.cs:113)
GameManager:OnReadyButtonClicked (ReadyButtonClickedEvent) (at Assets/Scripts/Manager/GameManager.cs:103)
EventBus:Publish<ReadyButtonClickedEvent> (ReadyButtonClickedEvent) (at Assets/Scripts/EventBus.cs:284)
UIManager:OnReadyButtonClicked () (at Assets/Scripts/Manager/UIManager.cs:83)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

Mevsim geçişi başladı: Yaz → Sonbahar
UnityEngine.Debug:Log (object)
GameManager/<SeasonTransitionCoroutine>d__21:MoveNext () (at Assets/Scripts/Manager/GameManager.cs:130)
UnityEngine.MonoBehaviour:StartCoroutine (System.Collections.IEnumerator)
GameManager:NextSeason () (at Assets/Scripts/Manager/GameManager.cs:113)
GameManager:OnReadyButtonClicked (ReadyButtonClickedEvent) (at Assets/Scripts/Manager/GameManager.cs:103)
EventBus:Publish<ReadyButtonClickedEvent> (ReadyButtonClickedEvent) (at Assets/Scripts/EventBus.cs:284)
UIManager:OnReadyButtonClicked () (at Assets/Scripts/Manager/UIManager.cs:83)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardPool: Bu kart zaten pool'da veya bu pool'a ait değil!
UnityEngine.Debug:LogWarning (object)
CardPool:ReturnCard (CardUI) (at Assets/Scripts/Pool/CardPool.cs:139)
CardController:ReturnAllDisplayedCards () (at Assets/Scripts/Controller/CardController.cs:212)
CardController:RefreshDisplayedCards () (at Assets/Scripts/Controller/CardController.cs:165)
CardController:OnSeasonChanged (SeasonChangedEvent) (at Assets/Scripts/Controller/CardController.cs:103)
EventBus:Publish<SeasonChangedEvent> (SeasonChangedEvent) (at Assets/Scripts/EventBus.cs:284)
GameManager:set_CurrentSeason (Season) (at Assets/Scripts/Manager/GameManager.cs:31)
GameManager/<SeasonTransitionCoroutine>d__21:MoveNext () (at Assets/Scripts/Manager/GameManager.cs:136)
UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr)

CardPool: Bu kart zaten pool'da veya bu pool'a ait değil!
UnityEngine.Debug:LogWarning (object)
CardPool:ReturnCard (CardUI) (at Assets/Scripts/Pool/CardPool.cs:139)
CardController:ReturnAllDisplayedCards () (at Assets/Scripts/Controller/CardController.cs:212)
CardController:RefreshDisplayedCards () (at Assets/Scripts/Controller/CardController.cs:165)
CardController:OnSeasonChanged (SeasonChangedEvent) (at Assets/Scripts/Controller/CardController.cs:103)
EventBus:Publish<SeasonChangedEvent> (SeasonChangedEvent) (at Assets/Scripts/EventBus.cs:284)
GameManager:set_CurrentSeason (Season) (at Assets/Scripts/Manager/GameManager.cs:31)
GameManager/<SeasonTransitionCoroutine>d__21:MoveNext () (at Assets/Scripts/Manager/GameManager.cs:136)
UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr)

CardPool: Bu kart zaten pool'da veya bu pool'a ait değil!
UnityEngine.Debug:LogWarning (object)
CardPool:ReturnCard (CardUI) (at Assets/Scripts/Pool/CardPool.cs:139)
CardController:ReturnAllDisplayedCards () (at Assets/Scripts/Controller/CardController.cs:212)
CardController:RefreshDisplayedCards () (at Assets/Scripts/Controller/CardController.cs:165)
CardController:OnSeasonChanged (SeasonChangedEvent) (at Assets/Scripts/Controller/CardController.cs:103)
EventBus:Publish<SeasonChangedEvent> (SeasonChangedEvent) (at Assets/Scripts/EventBus.cs:284)
GameManager:set_CurrentSeason (Season) (at Assets/Scripts/Manager/GameManager.cs:31)
GameManager/<SeasonTransitionCoroutine>d__21:MoveNext () (at Assets/Scripts/Manager/GameManager.cs:136)
UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr)

CardPool: Bu kart zaten pool'da veya bu pool'a ait değil!
UnityEngine.Debug:LogWarning (object)
CardPool:ReturnCard (CardUI) (at Assets/Scripts/Pool/CardPool.cs:139)
CardController:ReturnAllDisplayedCards () (at Assets/Scripts/Controller/CardController.cs:212)
CardController:RefreshDisplayedCards () (at Assets/Scripts/Controller/CardController.cs:165)
CardController:OnSeasonChanged (SeasonChangedEvent) (at Assets/Scripts/Controller/CardController.cs:103)
EventBus:Publish<SeasonChangedEvent> (SeasonChangedEvent) (at Assets/Scripts/EventBus.cs:284)
GameManager:set_CurrentSeason (Season) (at Assets/Scripts/Manager/GameManager.cs:31)
GameManager/<SeasonTransitionCoroutine>d__21:MoveNext () (at Assets/Scripts/Manager/GameManager.cs:136)
UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr)

CardPool: Bu kart zaten pool'da veya bu pool'a ait değil!
UnityEngine.Debug:LogWarning (object)
CardPool:ReturnCard (CardUI) (at Assets/Scripts/Pool/CardPool.cs:139)
CardController:ReturnAllDisplayedCards () (at Assets/Scripts/Controller/CardController.cs:212)
CardController:RefreshDisplayedCards () (at Assets/Scripts/Controller/CardController.cs:165)
CardController:OnSeasonChanged (SeasonChangedEvent) (at Assets/Scripts/Controller/CardController.cs:103)
EventBus:Publish<SeasonChangedEvent> (SeasonChangedEvent) (at Assets/Scripts/EventBus.cs:284)
GameManager:set_CurrentSeason (Season) (at Assets/Scripts/Manager/GameManager.cs:31)
GameManager/<SeasonTransitionCoroutine>d__21:MoveNext () (at Assets/Scripts/Manager/GameManager.cs:136)
UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr)

Mevsim geçişi tamamlandı - Ready butonu aktif
UnityEngine.Debug:Log (object)
UIManager:OnSeasonTransitionStateChanged (SeasonTransitionStateChangedEvent) (at Assets/Scripts/Manager/UIManager.cs:136)
EventBus:Publish<SeasonTransitionStateChangedEvent> (SeasonTransitionStateChangedEvent) (at Assets/Scripts/EventBus.cs:284)
GameManager:set_IsTransitioning (bool) (at Assets/Scripts/Manager/GameManager.cs:46)
GameManager/<SeasonTransitionCoroutine>d__21:MoveNext () (at Assets/Scripts/Manager/GameManager.cs:137)
UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr)

Mevsim geçişi tamamlandı: Sonbahar
UnityEngine.Debug:Log (object)
GameManager/<SeasonTransitionCoroutine>d__21:MoveNext () (at Assets/Scripts/Manager/GameManager.cs:139)
UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr)

CardUI: CardSelectedEvent gönderiliyor - currentCard null mu? False
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:121)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Kart adı: Corn 
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:124)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: OnCardSelected çağrıldı
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:74)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: eventData.Card null mu? False
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:75)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: eventData.CardUI null mu? False
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:76)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: Kart seçildi - Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:89)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Corn  kartı seçildi! Event gönderildi.
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:129)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Corn  kartı tıklandı! Seçili: True
UnityEngine.Debug:Log (object)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:98)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: Hex tıklandı - State: Boş
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:127)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Obje yerleştiriliyor...
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:133)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: PlaceObjectOnHex çağrıldı
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:199)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: selectedCard null mu? False
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:200)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: hex null mu? False
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:201)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Kart adı: Corn 
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:221)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Corn  objesi hex'e yerleştirildi!
UnityEngine.Debug:Log (object)
PlacementController:OnObjectPlacedOnHex (ObjectPlacedOnHexEvent) (at Assets/Scripts/Controller/PlacementController.cs:154)
EventBus:Publish<ObjectPlacedOnHexEvent> (ObjectPlacedOnHexEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:284)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:224)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Corn  kartı pool'a döndürüldü
UnityEngine.Debug:Log (object)
PlacementController:OnObjectPlacedOnHex (ObjectPlacedOnHexEvent) (at Assets/Scripts/Controller/PlacementController.cs:165)
EventBus:Publish<ObjectPlacedOnHexEvent> (ObjectPlacedOnHexEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:284)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:224)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Corn  objesi hex'e yerleştirildi!
UnityEngine.Debug:Log (object)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:286)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:224)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: NULL CARD başarıyla yerleştirildi!
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:229)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu, Position: (0.00, -0.73, -2.09), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Boş
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: False
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Önce bir kart seçin!
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:142)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Boş, Position: (0.00, -0.73, -2.09), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

CardUI: CardSelectedEvent gönderiliyor - currentCard null mu? False
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:121)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Kart adı: Corn 
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:124)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: OnCardSelected çağrıldı
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:74)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: eventData.Card null mu? False
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:75)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: eventData.CardUI null mu? False
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:76)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: Kart seçildi - Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:89)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Corn  kartı seçildi! Event gönderildi.
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:129)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Corn  kartı tıklandı! Seçili: True
UnityEngine.Debug:Log (object)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:98)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: Hex tıklandı - State: Boş
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:127)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Obje yerleştiriliyor...
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:133)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: PlaceObjectOnHex çağrıldı
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:199)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: selectedCard null mu? False
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:200)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: hex null mu? False
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:201)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Kart adı: Corn 
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:221)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Corn  objesi hex'e yerleştirildi!
UnityEngine.Debug:Log (object)
PlacementController:OnObjectPlacedOnHex (ObjectPlacedOnHexEvent) (at Assets/Scripts/Controller/PlacementController.cs:154)
EventBus:Publish<ObjectPlacedOnHexEvent> (ObjectPlacedOnHexEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:284)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:224)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Corn  kartı pool'a döndürüldü
UnityEngine.Debug:Log (object)
PlacementController:OnObjectPlacedOnHex (ObjectPlacedOnHexEvent) (at Assets/Scripts/Controller/PlacementController.cs:165)
EventBus:Publish<ObjectPlacedOnHexEvent> (ObjectPlacedOnHexEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:284)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:224)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Corn  objesi hex'e yerleştirildi!
UnityEngine.Debug:Log (object)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:286)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:224)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: NULL CARD başarıyla yerleştirildi!
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:229)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu, Position: (0.00, -0.73, 0.00), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

CardUI: CardSelectedEvent gönderiliyor - currentCard null mu? False
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:121)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Kart adı: Corn 
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:124)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: OnCardSelected çağrıldı
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:74)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: eventData.Card null mu? False
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:75)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: eventData.CardUI null mu? False
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:76)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: Kart seçildi - Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:89)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Corn  kartı seçildi! Event gönderildi.
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:129)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Corn  kartı tıklandı! Seçili: True
UnityEngine.Debug:Log (object)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:98)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: Hex tıklandı - State: Boş
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:127)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Obje yerleştiriliyor...
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:133)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: PlaceObjectOnHex çağrıldı
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:199)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: selectedCard null mu? False
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:200)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: hex null mu? False
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:201)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Kart adı: Corn 
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:221)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Corn  objesi hex'e yerleştirildi!
UnityEngine.Debug:Log (object)
PlacementController:OnObjectPlacedOnHex (ObjectPlacedOnHexEvent) (at Assets/Scripts/Controller/PlacementController.cs:154)
EventBus:Publish<ObjectPlacedOnHexEvent> (ObjectPlacedOnHexEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:284)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:224)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Corn  kartı pool'a döndürüldü
UnityEngine.Debug:Log (object)
PlacementController:OnObjectPlacedOnHex (ObjectPlacedOnHexEvent) (at Assets/Scripts/Controller/PlacementController.cs:165)
EventBus:Publish<ObjectPlacedOnHexEvent> (ObjectPlacedOnHexEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:284)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:224)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Corn  objesi hex'e yerleştirildi!
UnityEngine.Debug:Log (object)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:286)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:224)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: NULL CARD başarıyla yerleştirildi!
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:229)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu, Position: (-3.62, -0.73, 2.09), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Boş
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: False
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Önce bir kart seçin!
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:142)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Boş, Position: (-3.62, -0.73, 2.09), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

CardUI: CardSelectedEvent gönderiliyor - currentCard null mu? False
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:121)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Kart adı: Corn 
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:124)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: OnCardSelected çağrıldı
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:74)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: eventData.Card null mu? False
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:75)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: eventData.CardUI null mu? False
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:76)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: Kart seçildi - Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:89)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Corn  kartı seçildi! Event gönderildi.
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:129)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Corn  kartı tıklandı! Seçili: True
UnityEngine.Debug:Log (object)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:98)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: Hex tıklandı - State: Boş
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:127)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Obje yerleştiriliyor...
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:133)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: PlaceObjectOnHex çağrıldı
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:199)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: selectedCard null mu? False
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:200)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: hex null mu? False
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:201)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Kart adı: Corn 
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:221)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Corn  objesi hex'e yerleştirildi!
UnityEngine.Debug:Log (object)
PlacementController:OnObjectPlacedOnHex (ObjectPlacedOnHexEvent) (at Assets/Scripts/Controller/PlacementController.cs:154)
EventBus:Publish<ObjectPlacedOnHexEvent> (ObjectPlacedOnHexEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:284)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:224)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Corn  kartı pool'a döndürüldü
UnityEngine.Debug:Log (object)
PlacementController:OnObjectPlacedOnHex (ObjectPlacedOnHexEvent) (at Assets/Scripts/Controller/PlacementController.cs:165)
EventBus:Publish<ObjectPlacedOnHexEvent> (ObjectPlacedOnHexEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:284)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:224)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Corn  objesi hex'e yerleştirildi!
UnityEngine.Debug:Log (object)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:286)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:224)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: NULL CARD başarıyla yerleştirildi!
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:229)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu, Position: (1.81, -0.73, -3.13), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Boş
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: False
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Önce bir kart seçin!
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:142)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Boş, Position: (1.81, -0.73, -3.13), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

CardUI: Corn  kartının seçimi kaldırıldı!
UnityEngine.Debug:Log (object)
CardUI:DeselectCard () (at Assets/Scripts/UI/CardUI.cs:148)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:91)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Corn  kartı tıklandı! Seçili: False
UnityEngine.Debug:Log (object)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:98)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: CardSelectedEvent gönderiliyor - currentCard null mu? False
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:121)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Kart adı: Corn 
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:124)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: OnCardSelected çağrıldı
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:74)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: eventData.Card null mu? False
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:75)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: eventData.CardUI null mu? False
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:76)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: Kart seçildi - Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:89)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Corn  kartı seçildi! Event gönderildi.
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:129)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Corn  kartı tıklandı! Seçili: True
UnityEngine.Debug:Log (object)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:98)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: Hex tıklandı - State: Boş
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:127)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Obje yerleştiriliyor...
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:133)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: PlaceObjectOnHex çağrıldı
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:199)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: selectedCard null mu? False
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:200)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: hex null mu? False
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:201)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Kart adı: Corn 
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:221)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Corn  objesi hex'e yerleştirildi!
UnityEngine.Debug:Log (object)
PlacementController:OnObjectPlacedOnHex (ObjectPlacedOnHexEvent) (at Assets/Scripts/Controller/PlacementController.cs:154)
EventBus:Publish<ObjectPlacedOnHexEvent> (ObjectPlacedOnHexEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:284)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:224)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Corn  kartı pool'a döndürüldü
UnityEngine.Debug:Log (object)
PlacementController:OnObjectPlacedOnHex (ObjectPlacedOnHexEvent) (at Assets/Scripts/Controller/PlacementController.cs:165)
EventBus:Publish<ObjectPlacedOnHexEvent> (ObjectPlacedOnHexEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:284)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:224)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Corn  objesi hex'e yerleştirildi!
UnityEngine.Debug:Log (object)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:286)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:224)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: NULL CARD başarıyla yerleştirildi!
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:229)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu, Position: (1.81, -0.73, -1.04), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Boş
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: False
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Önce bir kart seçin!
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:142)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Boş, Position: (1.81, -0.73, -1.04), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

Mevsim geçişi başladı - Geri sayım başlatıldı
UnityEngine.Debug:Log (object)
UIManager:OnSeasonTransitionStateChanged (SeasonTransitionStateChangedEvent) (at Assets/Scripts/Manager/UIManager.cs:125)
EventBus:Publish<SeasonTransitionStateChangedEvent> (SeasonTransitionStateChangedEvent) (at Assets/Scripts/EventBus.cs:284)
GameManager:set_IsTransitioning (bool) (at Assets/Scripts/Manager/GameManager.cs:46)
GameManager/<SeasonTransitionCoroutine>d__21:MoveNext () (at Assets/Scripts/Manager/GameManager.cs:127)
UnityEngine.MonoBehaviour:StartCoroutine (System.Collections.IEnumerator)
GameManager:NextSeason () (at Assets/Scripts/Manager/GameManager.cs:113)
GameManager:OnReadyButtonClicked (ReadyButtonClickedEvent) (at Assets/Scripts/Manager/GameManager.cs:103)
EventBus:Publish<ReadyButtonClickedEvent> (ReadyButtonClickedEvent) (at Assets/Scripts/EventBus.cs:284)
UIManager:OnReadyButtonClicked () (at Assets/Scripts/Manager/UIManager.cs:83)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

Mevsim geçişi başladı: Sonbahar → Kış
UnityEngine.Debug:Log (object)
GameManager/<SeasonTransitionCoroutine>d__21:MoveNext () (at Assets/Scripts/Manager/GameManager.cs:130)
UnityEngine.MonoBehaviour:StartCoroutine (System.Collections.IEnumerator)
GameManager:NextSeason () (at Assets/Scripts/Manager/GameManager.cs:113)
GameManager:OnReadyButtonClicked (ReadyButtonClickedEvent) (at Assets/Scripts/Manager/GameManager.cs:103)
EventBus:Publish<ReadyButtonClickedEvent> (ReadyButtonClickedEvent) (at Assets/Scripts/EventBus.cs:284)
UIManager:OnReadyButtonClicked () (at Assets/Scripts/Manager/UIManager.cs:83)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardPool: Bu kart zaten pool'da veya bu pool'a ait değil!
UnityEngine.Debug:LogWarning (object)
CardPool:ReturnCard (CardUI) (at Assets/Scripts/Pool/CardPool.cs:139)
CardController:ReturnAllDisplayedCards () (at Assets/Scripts/Controller/CardController.cs:212)
CardController:RefreshDisplayedCards () (at Assets/Scripts/Controller/CardController.cs:165)
CardController:OnSeasonChanged (SeasonChangedEvent) (at Assets/Scripts/Controller/CardController.cs:103)
EventBus:Publish<SeasonChangedEvent> (SeasonChangedEvent) (at Assets/Scripts/EventBus.cs:284)
GameManager:set_CurrentSeason (Season) (at Assets/Scripts/Manager/GameManager.cs:31)
GameManager/<SeasonTransitionCoroutine>d__21:MoveNext () (at Assets/Scripts/Manager/GameManager.cs:136)
UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr)

CardPool: Bu kart zaten pool'da veya bu pool'a ait değil!
UnityEngine.Debug:LogWarning (object)
CardPool:ReturnCard (CardUI) (at Assets/Scripts/Pool/CardPool.cs:139)
CardController:ReturnAllDisplayedCards () (at Assets/Scripts/Controller/CardController.cs:212)
CardController:RefreshDisplayedCards () (at Assets/Scripts/Controller/CardController.cs:165)
CardController:OnSeasonChanged (SeasonChangedEvent) (at Assets/Scripts/Controller/CardController.cs:103)
EventBus:Publish<SeasonChangedEvent> (SeasonChangedEvent) (at Assets/Scripts/EventBus.cs:284)
GameManager:set_CurrentSeason (Season) (at Assets/Scripts/Manager/GameManager.cs:31)
GameManager/<SeasonTransitionCoroutine>d__21:MoveNext () (at Assets/Scripts/Manager/GameManager.cs:136)
UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr)

CardPool: Bu kart zaten pool'da veya bu pool'a ait değil!
UnityEngine.Debug:LogWarning (object)
CardPool:ReturnCard (CardUI) (at Assets/Scripts/Pool/CardPool.cs:139)
CardController:ReturnAllDisplayedCards () (at Assets/Scripts/Controller/CardController.cs:212)
CardController:RefreshDisplayedCards () (at Assets/Scripts/Controller/CardController.cs:165)
CardController:OnSeasonChanged (SeasonChangedEvent) (at Assets/Scripts/Controller/CardController.cs:103)
EventBus:Publish<SeasonChangedEvent> (SeasonChangedEvent) (at Assets/Scripts/EventBus.cs:284)
GameManager:set_CurrentSeason (Season) (at Assets/Scripts/Manager/GameManager.cs:31)
GameManager/<SeasonTransitionCoroutine>d__21:MoveNext () (at Assets/Scripts/Manager/GameManager.cs:136)
UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr)

CardPool: Bu kart zaten pool'da veya bu pool'a ait değil!
UnityEngine.Debug:LogWarning (object)
CardPool:ReturnCard (CardUI) (at Assets/Scripts/Pool/CardPool.cs:139)
CardController:ReturnAllDisplayedCards () (at Assets/Scripts/Controller/CardController.cs:212)
CardController:RefreshDisplayedCards () (at Assets/Scripts/Controller/CardController.cs:165)
CardController:OnSeasonChanged (SeasonChangedEvent) (at Assets/Scripts/Controller/CardController.cs:103)
EventBus:Publish<SeasonChangedEvent> (SeasonChangedEvent) (at Assets/Scripts/EventBus.cs:284)
GameManager:set_CurrentSeason (Season) (at Assets/Scripts/Manager/GameManager.cs:31)
GameManager/<SeasonTransitionCoroutine>d__21:MoveNext () (at Assets/Scripts/Manager/GameManager.cs:136)
UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr)

CardPool: Bu kart zaten pool'da veya bu pool'a ait değil!
UnityEngine.Debug:LogWarning (object)
CardPool:ReturnCard (CardUI) (at Assets/Scripts/Pool/CardPool.cs:139)
CardController:ReturnAllDisplayedCards () (at Assets/Scripts/Controller/CardController.cs:212)
CardController:RefreshDisplayedCards () (at Assets/Scripts/Controller/CardController.cs:165)
CardController:OnSeasonChanged (SeasonChangedEvent) (at Assets/Scripts/Controller/CardController.cs:103)
EventBus:Publish<SeasonChangedEvent> (SeasonChangedEvent) (at Assets/Scripts/EventBus.cs:284)
GameManager:set_CurrentSeason (Season) (at Assets/Scripts/Manager/GameManager.cs:31)
GameManager/<SeasonTransitionCoroutine>d__21:MoveNext () (at Assets/Scripts/Manager/GameManager.cs:136)
UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr)

Mevsim geçişi tamamlandı - Ready butonu aktif
UnityEngine.Debug:Log (object)
UIManager:OnSeasonTransitionStateChanged (SeasonTransitionStateChangedEvent) (at Assets/Scripts/Manager/UIManager.cs:136)
EventBus:Publish<SeasonTransitionStateChangedEvent> (SeasonTransitionStateChangedEvent) (at Assets/Scripts/EventBus.cs:284)
GameManager:set_IsTransitioning (bool) (at Assets/Scripts/Manager/GameManager.cs:46)
GameManager/<SeasonTransitionCoroutine>d__21:MoveNext () (at Assets/Scripts/Manager/GameManager.cs:137)
UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr)

Mevsim geçişi tamamlandı: Kış
UnityEngine.Debug:Log (object)
GameManager/<SeasonTransitionCoroutine>d__21:MoveNext () (at Assets/Scripts/Manager/GameManager.cs:139)
UnityEngine.SetupCoroutine:InvokeMoveNext (System.Collections.IEnumerator,intptr)

PlacementController: Hex tıklandı - State: Boş
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: False
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Önce bir kart seçin!
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:142)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Boş, Position: (-1.81, -0.73, 3.15), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Boş
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: False
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Önce bir kart seçin!
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:142)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Boş, Position: (-1.81, -0.73, 3.15), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Boş
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: False
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Önce bir kart seçin!
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:142)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Boş, Position: (-1.81, -0.73, 3.15), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Boş
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: False
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Önce bir kart seçin!
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:142)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Boş, Position: (-1.81, -0.73, 3.15), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

CardUI: Corn  kartının seçimi kaldırıldı!
UnityEngine.Debug:Log (object)
CardUI:DeselectCard () (at Assets/Scripts/UI/CardUI.cs:148)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:91)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Corn  kartı tıklandı! Seçili: False
UnityEngine.Debug:Log (object)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:98)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: Hex tıklandı - State: Boş
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: False
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Önce bir kart seçin!
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:142)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Boş, Position: (-1.81, -0.73, 3.15), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Boş
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: False
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Önce bir kart seçin!
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:142)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Boş, Position: (-1.81, -0.73, 3.15), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

CardUI: Corn  kartının seçimi kaldırıldı!
UnityEngine.Debug:Log (object)
CardUI:DeselectCard () (at Assets/Scripts/UI/CardUI.cs:148)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:91)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Corn  kartı tıklandı! Seçili: False
UnityEngine.Debug:Log (object)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:98)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: CardSelectedEvent gönderiliyor - currentCard null mu? False
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:121)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Kart adı: Corn 
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:124)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: OnCardSelected çağrıldı
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:74)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: eventData.Card null mu? False
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:75)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: eventData.CardUI null mu? False
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:76)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: Kart seçildi - Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:89)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:127)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Corn  kartı seçildi! Event gönderildi.
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:129)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Corn  kartı tıklandı! Seçili: True
UnityEngine.Debug:Log (object)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:98)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: Hex tıklandı - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:127)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Bu hex'e obje yerleştirilemez - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:138)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu, Position: (-1.81, -0.46, -1.04), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:127)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Bu hex'e obje yerleştirilemez - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:138)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu, Position: (-1.81, -0.46, -1.04), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:127)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Bu hex'e obje yerleştirilemez - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:138)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu, Position: (-1.81, -0.46, -1.04), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:127)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Bu hex'e obje yerleştirilemez - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:138)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu, Position: (-1.81, -0.46, -1.04), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:127)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Bu hex'e obje yerleştirilemez - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:138)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu, Position: (-1.81, -0.46, -1.04), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:127)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Bu hex'e obje yerleştirilemez - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:138)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu, Position: (-1.81, -0.46, -1.04), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Boş
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:127)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Obje yerleştiriliyor...
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:133)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: PlaceObjectOnHex çağrıldı
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:199)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: selectedCard null mu? False
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:200)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: hex null mu? False
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:201)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Kart adı: Corn 
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:221)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Corn  objesi hex'e yerleştirildi!
UnityEngine.Debug:Log (object)
PlacementController:OnObjectPlacedOnHex (ObjectPlacedOnHexEvent) (at Assets/Scripts/Controller/PlacementController.cs:154)
EventBus:Publish<ObjectPlacedOnHexEvent> (ObjectPlacedOnHexEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:284)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:224)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Corn  kartı pool'a döndürüldü
UnityEngine.Debug:Log (object)
PlacementController:OnObjectPlacedOnHex (ObjectPlacedOnHexEvent) (at Assets/Scripts/Controller/PlacementController.cs:165)
EventBus:Publish<ObjectPlacedOnHexEvent> (ObjectPlacedOnHexEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:284)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:224)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Corn  objesi hex'e yerleştirildi!
UnityEngine.Debug:Log (object)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:286)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:224)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: NULL CARD başarıyla yerleştirildi!
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:229)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:134)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu, Position: (0.00, -0.73, 2.09), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Boş
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:122)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: False
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:123)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Önce bir kart seçin!
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:142)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Boş, Position: (0.00, -0.73, 2.09), Event gönderildi!
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

CardUI: Corn  kartının seçimi kaldırıldı!
UnityEngine.Debug:Log (object)
CardUI:DeselectCard () (at Assets/Scripts/UI/CardUI.cs:148)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:91)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Corn  kartı tıklandı! Seçili: False
UnityEngine.Debug:Log (object)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:98)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

