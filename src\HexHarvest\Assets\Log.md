PlacementController: Instance oluşturuldu
UnityEngine.Debug:Log (object)
PlacementController:Awake () (at Assets/Scripts/Controller/PlacementController.cs:34)
UnityEngine.GameObject:AddComponent<PlacementController> ()
GameManager:EnsurePlacementController () (at Assets/Scripts/Manager/GameManager.cs:178)
GameManager:Awake () (at Assets/Scripts/Manager/GameManager.cs:70)

GameManager: PlacementController otomatik olarak oluşturuldu
UnityEngine.Debug:Log (object)
GameManager:EnsurePlacementController () (at Assets/Scripts/Manager/GameManager.cs:179)
GameManager:Awake () (at Assets/Scripts/Manager/GameManager.cs:70)

PlacementController: Event listener'lar kuruldu ve aktif!
UnityEngine.Debug:Log (object)
PlacementController:Start () (at Assets/Scripts/Controller/PlacementController.cs:51)

PlacementController: Debug modu aktif
UnityEngine.Debug:Log (object)
PlacementController:Start () (at Assets/Scripts/Controller/PlacementController.cs:55)

PlacementController: OnCardSelected çağrıldı
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:76)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:121)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: Kart seçildi - Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:92)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:121)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Corn  kartı seçildi!
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:123)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Corn  kartı tıklandı! Seçili: True
UnityEngine.Debug:Log (object)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:98)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: Hex tıklandı - State: Boş
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:128)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:129)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:133)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Obje yerleştiriliyor...
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:142)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Corn  objesi hex'e yerleştirildi!
UnityEngine.Debug:Log (object)
PlacementController:OnObjectPlacedOnHex (ObjectPlacedOnHexEvent) (at Assets/Scripts/Controller/PlacementController.cs:170)
EventBus:Publish<ObjectPlacedOnHexEvent> (ObjectPlacedOnHexEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:285)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:248)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:144)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Corn  kartı pool'a döndürüldü
UnityEngine.Debug:Log (object)
PlacementController:OnObjectPlacedOnHex (ObjectPlacedOnHexEvent) (at Assets/Scripts/Controller/PlacementController.cs:181)
EventBus:Publish<ObjectPlacedOnHexEvent> (ObjectPlacedOnHexEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:285)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:248)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:144)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Corn  objesi hex'e yerleştirildi!
UnityEngine.Debug:Log (object)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:287)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:248)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:144)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Corn  başarıyla yerleştirildi!
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:252)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:144)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Boş
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:128)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: False
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:129)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Önce bir kart seçin!
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:157)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Boş
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: OnCardSelected çağrıldı
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:76)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:121)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: Kart seçildi - Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:92)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:121)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Corn  kartı seçildi!
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:123)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Corn  kartı tıklandı! Seçili: True
UnityEngine.Debug:Log (object)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:98)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: Hex tıklandı - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:128)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:129)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:133)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Bu hex'e obje yerleştirilemez - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:150)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Boş
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:128)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:129)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:133)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Obje yerleştiriliyor...
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:142)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Corn  objesi hex'e yerleştirildi!
UnityEngine.Debug:Log (object)
PlacementController:OnObjectPlacedOnHex (ObjectPlacedOnHexEvent) (at Assets/Scripts/Controller/PlacementController.cs:170)
EventBus:Publish<ObjectPlacedOnHexEvent> (ObjectPlacedOnHexEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:285)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:248)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:144)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Corn  kartı pool'a döndürüldü
UnityEngine.Debug:Log (object)
PlacementController:OnObjectPlacedOnHex (ObjectPlacedOnHexEvent) (at Assets/Scripts/Controller/PlacementController.cs:181)
EventBus:Publish<ObjectPlacedOnHexEvent> (ObjectPlacedOnHexEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:285)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:248)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:144)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Corn  objesi hex'e yerleştirildi!
UnityEngine.Debug:Log (object)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:287)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:248)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:144)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Corn  başarıyla yerleştirildi!
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:252)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:144)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: OnCardSelected çağrıldı
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:76)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:121)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: Kart seçildi - Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:92)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:121)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Corn  kartı seçildi!
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:123)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Corn  kartı tıklandı! Seçili: True
UnityEngine.Debug:Log (object)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:98)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: Hex tıklandı - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:128)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:129)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:133)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Bu hex'e obje yerleştirilemez - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:150)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:128)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:129)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:133)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Bu hex'e obje yerleştirilemez - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:150)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:128)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:129)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:133)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Bu hex'e obje yerleştirilemez - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:150)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:128)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:129)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:133)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Bu hex'e obje yerleştirilemez - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:150)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:128)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:129)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:133)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Bu hex'e obje yerleştirilemez - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:150)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:128)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:129)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:133)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Bu hex'e obje yerleştirilemez - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:150)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Boş
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:128)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:129)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:133)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Obje yerleştiriliyor...
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:142)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Corn  objesi hex'e yerleştirildi!
UnityEngine.Debug:Log (object)
PlacementController:OnObjectPlacedOnHex (ObjectPlacedOnHexEvent) (at Assets/Scripts/Controller/PlacementController.cs:170)
EventBus:Publish<ObjectPlacedOnHexEvent> (ObjectPlacedOnHexEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:285)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:248)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:144)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Corn  kartı pool'a döndürüldü
UnityEngine.Debug:Log (object)
PlacementController:OnObjectPlacedOnHex (ObjectPlacedOnHexEvent) (at Assets/Scripts/Controller/PlacementController.cs:181)
EventBus:Publish<ObjectPlacedOnHexEvent> (ObjectPlacedOnHexEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:285)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:248)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:144)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Corn  objesi hex'e yerleştirildi!
UnityEngine.Debug:Log (object)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:287)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:248)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:144)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Corn  başarıyla yerleştirildi!
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:252)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:144)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Boş
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:128)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: False
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:129)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Önce bir kart seçin!
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:157)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Boş
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: OnCardSelected çağrıldı
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:76)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:121)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: Kart seçildi - Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnCardSelected (CardSelectedEvent) (at Assets/Scripts/Controller/PlacementController.cs:92)
EventBus:Publish<CardSelectedEvent> (CardSelectedEvent) (at Assets/Scripts/EventBus.cs:284)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:121)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Corn  kartı seçildi!
UnityEngine.Debug:Log (object)
CardUI:SelectCard () (at Assets/Scripts/UI/CardUI.cs:123)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:86)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

CardUI: Corn  kartı tıklandı! Seçili: True
UnityEngine.Debug:Log (object)
CardUI:OnCardClicked () (at Assets/Scripts/UI/CardUI.cs:98)
UnityEngine.EventSystems.EventSystem:Update () (at ./Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventSystem.cs:530)

PlacementController: Hex tıklandı - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:128)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:129)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:133)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Bu hex'e obje yerleştirilemez - State: Dolu
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:150)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Hex tıklandı - State: Boş
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:128)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: HasSelectedCard: True
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:129)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Seçili kart: Corn 
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:133)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Obje yerleştiriliyor...
UnityEngine.Debug:Log (object)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:142)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Corn  objesi hex'e yerleştirildi!
UnityEngine.Debug:Log (object)
PlacementController:OnObjectPlacedOnHex (ObjectPlacedOnHexEvent) (at Assets/Scripts/Controller/PlacementController.cs:170)
EventBus:Publish<ObjectPlacedOnHexEvent> (ObjectPlacedOnHexEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:285)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:248)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:144)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Corn  kartı pool'a döndürüldü
UnityEngine.Debug:Log (object)
PlacementController:OnObjectPlacedOnHex (ObjectPlacedOnHexEvent) (at Assets/Scripts/Controller/PlacementController.cs:181)
EventBus:Publish<ObjectPlacedOnHexEvent> (ObjectPlacedOnHexEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:285)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:248)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:144)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Corn  objesi hex'e yerleştirildi!
UnityEngine.Debug:Log (object)
BaseHex:PlaceObject (CardSO) (at Assets/Scripts/Hex/BaseHex.cs:287)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:248)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:144)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

PlacementController: Corn  başarıyla yerleştirildi!
UnityEngine.Debug:Log (object)
PlacementController:PlaceObjectOnHex (BaseHex) (at Assets/Scripts/Controller/PlacementController.cs:252)
PlacementController:OnHexClicked (HexClickedEvent) (at Assets/Scripts/Controller/PlacementController.cs:144)
EventBus:Publish<HexClickedEvent> (HexClickedEvent) (at Assets/Scripts/EventBus.cs:284)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:235)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

BaseHex: Hex tıklandı! State: Dolu
UnityEngine.Debug:Log (object)
BaseHex:OnMouseDown () (at Assets/Scripts/Hex/BaseHex.cs:237)
UnityEngine.SendMouseEvents:DoSendMouseEvents (int)

