using System;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// Base interface for all game events
/// </summary>
public interface IGameEvent
{
}

/// <summary>
/// Season değiştiğinde tetiklenen event
/// </summary>
public struct SeasonChangedEvent : IGameEvent
{
    public Season NewSeason { get; }
    public Season PreviousSeason { get; }

    public SeasonChangedEvent(Season newSeason, Season previousSeason)
    {
        NewSeason = newSeason;
        PreviousSeason = previousSeason;
    }
}

/// <summary>
/// Mevsim geçiş durumu değiştiğinde tetiklenen event
/// </summary>
public struct SeasonTransitionStateChangedEvent : IGameEvent
{
    public bool IsTransitioning { get; }
    public float TransitionDuration { get; }

    public SeasonTransitionStateChangedEvent(bool isTransitioning, float transitionDuration = 0f)
    {
        IsTransitioning = isTransitioning;
        TransitionDuration = transitionDuration;
    }
}

/// <summary>
/// Ready butonu tıklandığında tetiklenen event
/// </summary>
public struct ReadyButtonClickedEvent : IGameEvent
{
    public ReadyButtonClickedEvent(bool dummy = false)
    {
        // Struct için dummy parameter
    }
}

/// <summary>
/// Kart tıklandığında tetiklenen event
/// </summary>
public struct CardClickedEvent : IGameEvent
{
    public CardSO Card { get; }
    public Vector3 ClickPosition { get; }

    public CardClickedEvent(CardSO card, Vector3 clickPosition)
    {
        Card = card;
        ClickPosition = clickPosition;
    }
}

/// <summary>
/// Kart seçildiğinde tetiklenen event
/// </summary>
public struct CardSelectedEvent : IGameEvent
{
    public CardSO Card { get; }
    public CardUI CardUI { get; }

    public CardSelectedEvent(CardSO card, CardUI cardUI)
    {
        Card = card;
        CardUI = cardUI;
    }
}

/// <summary>
/// Kart seçimi iptal edildiğinde tetiklenen event
/// </summary>
public struct CardDeselectedEvent : IGameEvent
{
    public CardSO Card { get; }
    public CardUI CardUI { get; }

    public CardDeselectedEvent(CardSO card, CardUI cardUI)
    {
        Card = card;
        CardUI = cardUI;
    }
}

/// <summary>
/// Hex tıklandığında tetiklenen event
/// </summary>
public struct HexClickedEvent : IGameEvent
{
    public BaseHex Hex { get; }
    public Vector3 ClickPosition { get; }

    public HexClickedEvent(BaseHex hex, Vector3 clickPosition)
    {
        Hex = hex;
        ClickPosition = clickPosition;
    }
}

/// <summary>
/// Hex'e obje yerleştirildiğinde tetiklenen event
/// </summary>
public struct ObjectPlacedOnHexEvent : IGameEvent
{
    public BaseHex Hex { get; }
    public CardSO Card { get; }
    public GameObject PlacedObject { get; }

    public ObjectPlacedOnHexEvent(BaseHex hex, CardSO card, GameObject placedObject)
    {
        Hex = hex;
        Card = card;
        PlacedObject = placedObject;
    }
}

/// <summary>
/// Hex durumu değiştiğinde tetiklenen event
/// </summary>
public struct HexStateChangedEvent : IGameEvent
{
    public BaseHex Hex { get; }
    public BaseHex.HexState NewState { get; }
    public BaseHex.HexState PreviousState { get; }

    public HexStateChangedEvent(BaseHex hex, BaseHex.HexState newState, BaseHex.HexState previousState)
    {
        Hex = hex;
        NewState = newState;
        PreviousState = previousState;
    }
}

/// <summary>
/// Kart pool durumu değiştiğinde tetiklenen event
/// </summary>
public struct CardPoolStateChangedEvent : IGameEvent
{
    public int AvailableCount { get; }
    public int UsedCount { get; }
    public int TotalCount { get; }

    public CardPoolStateChangedEvent(int availableCount, int usedCount, int totalCount)
    {
        AvailableCount = availableCount;
        UsedCount = usedCount;
        TotalCount = totalCount;
    }
}

/// <summary>
/// Oyun başladığında tetiklenen event
/// </summary>
public struct GameStartedEvent : IGameEvent
{
    public Season InitialSeason { get; }

    public GameStartedEvent(Season initialSeason)
    {
        InitialSeason = initialSeason;
    }
}

/// <summary>
/// Oyun durduğunda tetiklenen event
/// </summary>
public struct GameStoppedEvent : IGameEvent
{
    public GameStoppedEvent(bool dummy = false)
    {
        // Struct için dummy parameter
    }
}

/// <summary>
/// Merkezi event sistemi - Tüm game eventlerini yönetir
/// </summary>
public static class EventBus
{
    // Event listener'ları saklayan dictionary
    private static readonly Dictionary<Type, List<Delegate>> eventListeners = new Dictionary<Type, List<Delegate>>();
    
    // Debug modu
    private static bool debugMode = false;

    /// <summary>
    /// Debug modunu açar/kapatır
    /// </summary>
    /// <param name="enabled">Debug modu aktif mi</param>
    public static void SetDebugMode(bool enabled)
    {
        debugMode = enabled;
    }

    /// <summary>
    /// Event'e listener ekler
    /// </summary>
    /// <typeparam name="T">Event türü</typeparam>
    /// <param name="listener">Listener fonksiyonu</param>
    public static void Subscribe<T>(Action<T> listener) where T : IGameEvent
    {
        Type eventType = typeof(T);
        
        if (!eventListeners.ContainsKey(eventType))
        {
            eventListeners[eventType] = new List<Delegate>();
        }
        
        eventListeners[eventType].Add(listener);
        
        if (debugMode)
        {
            Debug.Log($"EventBus: {eventType.Name} event'ine listener eklendi. Toplam listener: {eventListeners[eventType].Count}");
        }
    }

    /// <summary>
    /// Event'ten listener kaldırır
    /// </summary>
    /// <typeparam name="T">Event türü</typeparam>
    /// <param name="listener">Listener fonksiyonu</param>
    public static void Unsubscribe<T>(Action<T> listener) where T : IGameEvent
    {
        Type eventType = typeof(T);
        
        if (eventListeners.ContainsKey(eventType))
        {
            eventListeners[eventType].Remove(listener);
            
            // Eğer liste boşsa, dictionary'den kaldır
            if (eventListeners[eventType].Count == 0)
            {
                eventListeners.Remove(eventType);
            }
            
            if (debugMode)
            {
                int remainingCount = eventListeners.ContainsKey(eventType) ? eventListeners[eventType].Count : 0;
                Debug.Log($"EventBus: {eventType.Name} event'inden listener kaldırıldı. Kalan listener: {remainingCount}");
            }
        }
        else if (debugMode)
        {
            Debug.LogWarning($"EventBus: {eventType.Name} event'i için listener bulunamadı!");
        }
    }

    /// <summary>
    /// Event'i tetikler
    /// </summary>
    /// <typeparam name="T">Event türü</typeparam>
    /// <param name="eventData">Event verisi</param>
    public static void Publish<T>(T eventData) where T : IGameEvent
    {
        Type eventType = typeof(T);
        
        if (eventListeners.ContainsKey(eventType))
        {
            // Listener'ları kopyala (iteration sırasında değişiklik olmasını önlemek için)
            var listeners = new List<Delegate>(eventListeners[eventType]);
            
            if (debugMode)
            {
                Debug.Log($"EventBus: {eventType.Name} event'i tetiklendi. {listeners.Count} listener'a gönderiliyor.");
            }
            
            foreach (var listener in listeners)
            {
                try
                {
                    ((Action<T>)listener).Invoke(eventData);
                }
                catch (Exception ex)
                {
                    Debug.LogError($"EventBus: {eventType.Name} event listener'ında hata: {ex.Message}\n{ex.StackTrace}");
                }
            }
        }
        else if (debugMode)
        {
            Debug.Log($"EventBus: {eventType.Name} event'i için listener bulunamadı.");
        }
    }

    /// <summary>
    /// Tüm event listener'ları temizler
    /// </summary>
    public static void Clear()
    {
        int totalListeners = 0;
        foreach (var kvp in eventListeners)
        {
            totalListeners += kvp.Value.Count;
        }
        
        eventListeners.Clear();
        
        if (debugMode)
        {
            Debug.Log($"EventBus: Tüm event listener'lar temizlendi. Temizlenen listener sayısı: {totalListeners}");
        }
    }

    /// <summary>
    /// Belirli bir event türü için tüm listener'ları temizler
    /// </summary>
    /// <typeparam name="T">Event türü</typeparam>
    public static void Clear<T>() where T : IGameEvent
    {
        Type eventType = typeof(T);
        
        if (eventListeners.ContainsKey(eventType))
        {
            int listenerCount = eventListeners[eventType].Count;
            eventListeners.Remove(eventType);
            
            if (debugMode)
            {
                Debug.Log($"EventBus: {eventType.Name} event'i için tüm listener'lar temizlendi. Temizlenen: {listenerCount}");
            }
        }
    }

    /// <summary>
    /// Event sistemi istatistiklerini döndürür
    /// </summary>
    /// <returns>İstatistik bilgileri</returns>
    public static string GetStats()
    {
        int totalEvents = eventListeners.Count;
        int totalListeners = 0;
        
        foreach (var kvp in eventListeners)
        {
            totalListeners += kvp.Value.Count;
        }
        
        return $"EventBus Stats - Event Türleri: {totalEvents}, Toplam Listener: {totalListeners}";
    }

    /// <summary>
    /// Detaylı event istatistiklerini döndürür
    /// </summary>
    /// <returns>Detaylı istatistik bilgileri</returns>
    public static string GetDetailedStats()
    {
        if (eventListeners.Count == 0)
        {
            return "EventBus: Hiç event listener yok.";
        }

        string stats = "EventBus Detaylı İstatistikler:\n";

        foreach (var kvp in eventListeners)
        {
            stats += $"- {kvp.Key.Name}: {kvp.Value.Count} listener\n";
        }

        return stats.TrimEnd('\n');
    }
}
