using UnityEngine;

[System.Serializable]
public enum Season
{
    Spring,  // İlkbahar
    Summer,  // Yaz
    Autumn,  // Sonbahar
    Winter   // Kış
}

public static class Seasons
{
    /// <summary>
    /// Mevsimin <PERSON> ismini döndürür
    /// </summary>
    /// <param name="season">Mevsim</param>
    /// <returns>Mevsim adı</returns>
    public static string GetSeasonString(Season season)
    {
        switch (season)
        {
            case Season.Spring:
                return "İlkbahar";
            case Season.Summer:
                return "Yaz";
            case Season.Autumn:
                return "Sonbahar";
            case Season.Winter:
                return "Kış";
            default:
                return "Bilinmeyen";
        }
    }

    /// <summary>
    /// Mevsime göre renk döndürür
    /// </summary>
    /// <param name="season">Mevsim</param>
    /// <returns>Mevsim rengi</returns>
    public static Color GetSeasonColor(Season season)
    {
        switch (season)
        {
            case Season.Spring:
                return Color.green;
            case Season.Summer:
                return Color.yellow;
            case Season.Autumn:
                return new Color(1f, 0.5f, 0f); // Turuncu
            case Season.Winter:
                return Color.cyan;
            default:
                return Color.white;
        }
    }

    /// <summary>
    /// Mevsime göre hex rengi döndürür (daha koyu tonlar)
    /// </summary>
    /// <param name="season">Mevsim</param>
    /// <returns>Hex rengi</returns>
    public static Color GetSeasonHexColor(Season season)
    {
        switch (season)
        {
            case Season.Spring:
                return new Color(0f, 0.8f, 0f); // Koyu yeşil
            case Season.Summer:
                return new Color(1f, 0.8f, 0f); // Altın sarısı
            case Season.Autumn:
                return new Color(0.8f, 0.4f, 0f); // Koyu turuncu
            case Season.Winter:
                return new Color(0.7f, 0.9f, 1f); // Açık mavi
            default:
                return Color.gray;
        }
    }

    /// <summary>
    /// Sonraki mevsimi döndürür
    /// </summary>
    /// <param name="currentSeason">Mevcut mevsim</param>
    /// <returns>Sonraki mevsim</returns>
    public static Season GetNextSeason(Season currentSeason)
    {
        switch (currentSeason)
        {
            case Season.Spring:
                return Season.Summer;
            case Season.Summer:
                return Season.Autumn;
            case Season.Autumn:
                return Season.Winter;
            case Season.Winter:
                return Season.Spring;
            default:
                return Season.Spring;
        }
    }

    /// <summary>
    /// Önceki mevsimi döndürür
    /// </summary>
    /// <param name="currentSeason">Mevcut mevsim</param>
    /// <returns>Önceki mevsim</returns>
    public static Season GetPreviousSeason(Season currentSeason)
    {
        switch (currentSeason)
        {
            case Season.Spring:
                return Season.Winter;
            case Season.Summer:
                return Season.Spring;
            case Season.Autumn:
                return Season.Summer;
            case Season.Winter:
                return Season.Autumn;
            default:
                return Season.Winter;
        }
    }

    /// <summary>
    /// Tüm mevsimleri array olarak döndürür
    /// </summary>
    /// <returns>Mevsimler array'i</returns>
    public static Season[] GetAllSeasons()
    {
        return new Season[] { Season.Spring, Season.Summer, Season.Autumn, Season.Winter };
    }

    /// <summary>
    /// Rastgele bir mevsim döndürür
    /// </summary>
    /// <returns>Rastgele mevsim</returns>
    public static Season GetRandomSeason()
    {
        Season[] seasons = GetAllSeasons();
        return seasons[Random.Range(0, seasons.Length)];
    }

    /// <summary>
    /// İki mevsim arasındaki farkı döndürür (0-3 arası)
    /// </summary>
    /// <param name="season1">İlk mevsim</param>
    /// <param name="season2">İkinci mevsim</param>
    /// <returns>Mevsim farkı</returns>
    public static int GetSeasonDistance(Season season1, Season season2)
    {
        int diff = Mathf.Abs((int)season1 - (int)season2);
        return Mathf.Min(diff, 4 - diff); // Döngüsel mesafe
    }

    /// <summary>
    /// Belirtilen mevsimin sıcaklık değerini döndürür (0-100 arası)
    /// </summary>
    /// <param name="season">Mevsim</param>
    /// <returns>Sıcaklık değeri</returns>
    public static int GetSeasonTemperature(Season season)
    {
        switch (season)
        {
            case Season.Spring:
                return 60; // Ilıman
            case Season.Summer:
                return 85; // Sıcak
            case Season.Autumn:
                return 45; // Serin
            case Season.Winter:
                return 20; // Soğuk
            default:
                return 50;
        }
    }
}
