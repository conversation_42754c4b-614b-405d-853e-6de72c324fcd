using UnityEngine;

/// <summary>
/// Season değiştiğinde tetiklenen event
/// </summary>
public struct SeasonChangedEvent : IGameEvent
{
    public Season NewSeason { get; }
    public Season PreviousSeason { get; }

    public SeasonChangedEvent(Season newSeason, Season previousSeason)
    {
        NewSeason = newSeason;
        PreviousSeason = previousSeason;
    }
}

/// <summary>
/// Mevsim geçiş durumu değiştiğinde tetiklenen event
/// </summary>
public struct SeasonTransitionStateChangedEvent : IGameEvent
{
    public bool IsTransitioning { get; }
    public float TransitionDuration { get; }

    public SeasonTransitionStateChangedEvent(bool isTransitioning, float transitionDuration = 0f)
    {
        IsTransitioning = isTransitioning;
        TransitionDuration = transitionDuration;
    }
}

/// <summary>
/// Ready butonu tıklandığında tetiklenen event
/// </summary>
public struct ReadyButtonClickedEvent : IGameEvent
{
    public ReadyButtonClickedEvent(bool dummy = false)
    {
        // Struct için dummy parameter
    }
}

/// <summary>
/// Kart tıklandığında tetiklenen event
/// </summary>
public struct CardClickedEvent : IGameEvent
{
    public CardSO Card { get; }
    public Vector3 ClickPosition { get; }

    public CardClickedEvent(CardSO card, Vector3 clickPosition)
    {
        Card = card;
        ClickPosition = clickPosition;
    }
}

/// <summary>
/// Hex durumu değiştiğinde tetiklenen event
/// </summary>
public struct HexStateChangedEvent : IGameEvent
{
    public BaseHex Hex { get; }
    public HexState NewState { get; }
    public HexState PreviousState { get; }

    public HexStateChangedEvent(BaseHex hex, HexState newState, HexState previousState)
    {
        Hex = hex;
        NewState = newState;
        PreviousState = previousState;
    }
}

/// <summary>
/// Kart pool durumu değiştiğinde tetiklenen event
/// </summary>
public struct CardPoolStateChangedEvent : IGameEvent
{
    public int AvailableCount { get; }
    public int UsedCount { get; }
    public int TotalCount { get; }

    public CardPoolStateChangedEvent(int availableCount, int usedCount, int totalCount)
    {
        AvailableCount = availableCount;
        UsedCount = usedCount;
        TotalCount = totalCount;
    }
}

/// <summary>
/// Oyun başladığında tetiklenen event
/// </summary>
public struct GameStartedEvent : IGameEvent
{
    public Season InitialSeason { get; }

    public GameStartedEvent(Season initialSeason)
    {
        InitialSeason = initialSeason;
    }
}

/// <summary>
/// Oyun durduğunda tetiklenen event
/// </summary>
public struct GameStoppedEvent : IGameEvent
{
    public GameStoppedEvent(bool dummy = false)
    {
        // Struct için dummy parameter
    }
}
