using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections;

public class UIManager : MonoBehaviour
{
    [Header("Season UI")]
    [SerializeField] private Button readyButton;
    [SerializeField] private TextMeshProUGUI seasonText;
    
    [<PERSON>er("UI Settings")]
    [SerializeField] private bool updateSeasonTextColor = true;
    [SerializeField] private string seasonTextPrefix = "Season: ";

    [Header("Button Settings")]
    [SerializeField] private string defaultButtonText = "Ready";

    // Private variables
    private TextMeshProUGUI readyButtonText;
    private Coroutine countdownCoroutine;
    
    // Singleton pattern
    public static UIManager Instance { get; private set; }

    private void Awake()
    {
        // Singleton setup
        if (Instance == null)
        {
            Instance = this;
        }
        else
        {
            Destroy(gameObject);
        }
    }

    private void Start()
    {
        // Button event'ini bağla ve text referansını al
        if (readyButton != null)
        {
            readyButton.onClick.AddListener(OnReadyButtonClicked);
            readyButtonText = readyButton.GetComponentInChildren<TextMeshProUGUI>();

            if (readyButtonText != null)
            {
                readyButtonText.text = defaultButtonText;
            }
            else
            {
                Debug.LogWarning("Ready Button'da TextMeshProUGUI child bulunamadı!");
            }
        }
        
        // GameManager event'lerini dinle
        if (GameManager.Instance != null)
        {
            GameManager.Instance.OnSeasonChanged.AddListener(UpdateSeasonUI);
            GameManager.Instance.OnSeasonTransitionStateChanged.AddListener(OnSeasonTransitionStateChanged);

            // İlk güncelleme
            UpdateSeasonUI(GameManager.Instance.CurrentSeason);
            OnSeasonTransitionStateChanged(GameManager.Instance.IsTransitioning);
        }
        else
        {
            Debug.LogWarning("GameManager bulunamadı! UIManager düzgün çalışmayabilir.");
        }
    }

    private void OnDestroy()
    {
        // Event'leri temizle
        if (readyButton != null)
        {
            readyButton.onClick.RemoveListener(OnReadyButtonClicked);
        }
        
        if (GameManager.Instance != null)
        {
            GameManager.Instance.OnSeasonChanged.RemoveListener(UpdateSeasonUI);
            GameManager.Instance.OnSeasonTransitionStateChanged.RemoveListener(OnSeasonTransitionStateChanged);
        }
    }

    /// <summary>
    /// Ready butonu tıklandığında çağrılır
    /// </summary>
    private void OnReadyButtonClicked()
    {
        if (GameManager.Instance != null)
        {
            GameManager.Instance.NextSeason();
        }
        else
        {
            Debug.LogError("GameManager bulunamadı!");
        }
    }

    /// <summary>
    /// Mevsim geçiş durumu değiştiğinde çağrılır
    /// </summary>
    /// <param name="isTransitioning">Geçiş durumunda mı</param>
    private void OnSeasonTransitionStateChanged(bool isTransitioning)
    {
        // Ready butonunu geçiş durumuna göre aktif/pasif yap
        SetReadyButtonActive(!isTransitioning);

        if (isTransitioning)
        {
            // Geri sayım başlat
            if (countdownCoroutine != null)
            {
                StopCoroutine(countdownCoroutine);
            }
            countdownCoroutine = StartCoroutine(CountdownCoroutine());
            Debug.Log("Mevsim geçişi başladı - Geri sayım başlatıldı");
        }
        else
        {
            // Geri sayımı durdur ve button text'ini sıfırla
            if (countdownCoroutine != null)
            {
                StopCoroutine(countdownCoroutine);
                countdownCoroutine = null;
            }
            SetReadyButtonText(defaultButtonText);
            Debug.Log("Mevsim geçişi tamamlandı - Ready butonu aktif");
        }
    }

    /// <summary>
    /// Mevsim UI'ını günceller
    /// </summary>
    /// <param name="newSeason">Yeni mevsim</param>
    private void UpdateSeasonUI(Season newSeason)
    {
        if (seasonText != null)
        {
            // Mevsim metnini güncelle
            string seasonName = Seasons.GetSeasonString(newSeason);
            seasonText.text = seasonTextPrefix + seasonName;

            // Mevsim rengini güncelle
            if (updateSeasonTextColor)
            {
                seasonText.color = Seasons.GetSeasonColor(newSeason);
            }
        }
        else
        {
            Debug.LogWarning("Season Text referansı atanmamış!");
        }
    }

    /// <summary>
    /// Ready butonunu aktif/pasif yapar
    /// </summary>
    /// <param name="isActive">Aktif mi</param>
    public void SetReadyButtonActive(bool isActive)
    {
        if (readyButton != null)
        {
            readyButton.interactable = isActive;
        }
    }

    /// <summary>
    /// Geri sayım coroutine'i
    /// </summary>
    /// <returns></returns>
    private IEnumerator CountdownCoroutine()
    {
        if (GameManager.Instance == null) yield break;

        // GameManager'dan geçiş süresini al
        float totalTime = GameManager.Instance.SeasonTransitionDuration;
        float remainingTime = totalTime;

        while (remainingTime > 0)
        {
            // Geri sayımı göster (tam saniye)
            int seconds = Mathf.CeilToInt(remainingTime);
            SetReadyButtonText(seconds.ToString());

            // 1 saniye bekle
            yield return new WaitForSeconds(1f);
            remainingTime -= 1f;
        }

        // Geri sayım bittiğinde Ready text'ini göster
        SetReadyButtonText(defaultButtonText);
    }

    /// <summary>
    /// Ready buton metnini değiştirir
    /// </summary>
    /// <param name="newText">Yeni metin</param>
    public void SetReadyButtonText(string newText)
    {
        if (readyButtonText != null)
        {
            readyButtonText.text = newText;
        }
        else if (readyButton != null)
        {
            // Fallback: Tekrar referansı bulmaya çalış
            TextMeshProUGUI buttonText = readyButton.GetComponentInChildren<TextMeshProUGUI>();
            if (buttonText != null)
            {
                buttonText.text = newText;
            }
        }
    }

    /// <summary>
    /// Mevsim metnini manuel olarak günceller
    /// </summary>
    /// <param name="customText">Özel metin</param>
    public void SetSeasonText(string customText)
    {
        if (seasonText != null)
        {
            seasonText.text = customText;
        }
    }

    /// <summary>
    /// Mevsim metninin rengini değiştirir
    /// </summary>
    /// <param name="color">Yeni renk</param>
    public void SetSeasonTextColor(Color color)
    {
        if (seasonText != null)
        {
            seasonText.color = color;
        }
    }

    /// <summary>
    /// UI'ı tamamen yeniler
    /// </summary>
    public void RefreshUI()
    {
        if (GameManager.Instance != null)
        {
            UpdateSeasonUI(GameManager.Instance.CurrentSeason);
        }
    }

    // Inspector'da test için
    [ContextMenu("Test - Next Season")]
    private void TestNextSeason()
    {
        OnReadyButtonClicked();
    }

    [ContextMenu("Test - Refresh UI")]
    private void TestRefreshUI()
    {
        RefreshUI();
    }
}
