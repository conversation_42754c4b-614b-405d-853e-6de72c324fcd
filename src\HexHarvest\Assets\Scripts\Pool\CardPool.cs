using System.Collections.Generic;
using UnityEngine;

public class CardPool : MonoBehaviour
{
    [Header("Pool Settings")]
    [SerializeField] private GameObject cardUIPrefab; // CardUI prefab'ı (CardSO'dan bağımsız)
    [SerializeField] private int initialPoolSize = 10;
    
    [Header("Debug")]
    [SerializeField] private bool showDebugInfo = false;
    
    // Pool collections
    private Queue<CardUI> availableCards = new Queue<CardUI>();
    private List<CardUI> usedCards = new List<CardUI>();
    private Transform poolContainer; // Pool'un kendi container'ı

    // Singleton pattern
    
    // Properties
    public int AvailableCount => availableCards.Count;
    public int UsedCount => usedCards.Count;
    public int TotalCount => AvailableCount + UsedCount;

    private void Awake()
    {
        // Pool container'ını oluştur
        GameObject poolContainerObject = new GameObject("CardPool_Container");
        poolContainerObject.transform.SetParent(transform);
        poolContainer = poolContainerObject.transform;
    }

    private void Start()
    {
        InitializePool();
    }

    /// <summary>
    /// Pool'u başlatır
    /// </summary>
    private void InitializePool()
    {
        if (cardUIPrefab == null)
        {
            Debug.LogError("CardPool: Card UI Prefab atanmamış!");
            return;
        }

        // Başlangıç kartlarını oluştur
        for (int i = 0; i < initialPoolSize; i++)
        {
            CreateNewCard();
        }
        
        if (showDebugInfo)
        {
            Debug.Log($"CardPool: {initialPoolSize} kart ile başlatıldı");
        }
    }

    /// <summary>
    /// Yeni bir kart oluşturur ve pool'a ekler
    /// </summary>
    /// <returns>Oluşturulan CardUI</returns>
    private CardUI CreateNewCard()
    {
        GameObject cardObject = Instantiate(cardUIPrefab, poolContainer);
        CardUI cardUI = cardObject.GetComponent<CardUI>();
        
        if (cardUI == null)
        {
            Debug.LogError("CardPool: Prefab'da CardUI component'i bulunamadı!");
            Destroy(cardObject);
            return null;
        }
        
        // Kartı pool'a hazırla
        cardUI.PrepareForPool();
        availableCards.Enqueue(cardUI);
        
        return cardUI;
    }

    /// <summary>
    /// Pool'dan bir kart alır
    /// </summary>
    /// <param name="parent">Kartın yerleştirileceği parent (null ise pool container'da kalır)</param>
    /// <returns>Kullanıma hazır CardUI</returns>
    public CardUI GetCard(Transform parent = null)
    {
        CardUI card;

        // Eğer pool'da kart yoksa yeni oluştur
        if (availableCards.Count == 0)
        {
            card = CreateNewCard();
            if (card == null) return null;

            if (showDebugInfo)
            {
                Debug.Log("CardPool: Yeni kart oluşturuldu (pool boştu)");
            }
        }
        else
        {
            card = availableCards.Dequeue();
        }

        // Kartı kullanıma hazırla - parent belirtilmişse o parent'a taşı
        Transform targetParent = parent != null ? parent : poolContainer;
        card.PrepareForUse(targetParent);
        usedCards.Add(card);

        // EventBus ile pool durumu değişikliğini bildir
        EventBus.Publish(new CardPoolStateChangedEvent(AvailableCount, UsedCount, TotalCount));

        if (showDebugInfo)
        {
            Debug.Log($"CardPool: Kart alındı. Kullanılan: {UsedCount}, Mevcut: {AvailableCount}");
        }

        return card;
    }

    /// <summary>
    /// Kartı pool'a geri döndürür
    /// </summary>
    /// <param name="card">Döndürülecek kart</param>
    public void ReturnCard(CardUI card)
    {
        if (card == null)
        {
            Debug.LogWarning("CardPool: Null kart döndürülmeye çalışıldı!");
            return;
        }
        
        if (!usedCards.Contains(card))
        {
            Debug.LogWarning("CardPool: Bu kart zaten pool'da veya bu pool'a ait değil!");
            return;
        }
        
        // Kartı kullanılan listesinden çıkar
        usedCards.Remove(card);
        
        // Kartı pool'a hazırla
        card.PrepareForPool();
        card.transform.SetParent(poolContainer);
        
        // Pool'a geri ekle
        availableCards.Enqueue(card);

        // EventBus ile pool durumu değişikliğini bildir
        EventBus.Publish(new CardPoolStateChangedEvent(AvailableCount, UsedCount, TotalCount));

        if (showDebugInfo)
        {
            Debug.Log($"CardPool: Kart döndürüldü. Kullanılan: {UsedCount}, Mevcut: {AvailableCount}");
        }
    }

    /// <summary>
    /// Tüm kullanılan kartları pool'a döndürür
    /// </summary>
    public void ReturnAllCards()
    {
        // Kullanılan kartların kopyasını al (liste değişeceği için)
        List<CardUI> cardsToReturn = new List<CardUI>(usedCards);
        
        foreach (CardUI card in cardsToReturn)
        {
            ReturnCard(card);
        }
        
        if (showDebugInfo)
        {
            Debug.Log($"CardPool: Tüm kartlar döndürüldü. Toplam: {TotalCount}");
        }
    }

    /// <summary>
    /// Belirtilen sayıda kart ön-oluşturur
    /// </summary>
    /// <param name="count">Oluşturulacak kart sayısı</param>
    public void PrewarmPool(int count)
    {
        for (int i = 0; i < count; i++)
        {
            CreateNewCard();
        }
        
        if (showDebugInfo)
        {
            Debug.Log($"CardPool: {count} kart ön-oluşturuldu. Toplam: {TotalCount}");
        }
    }

    /// <summary>
    /// Pool istatistiklerini döndürür
    /// </summary>
    /// <returns>Pool bilgileri</returns>
    public string GetPoolStats()
    {
        return $"CardPool - Toplam: {TotalCount}, Kullanılan: {UsedCount}, Mevcut: {AvailableCount}";
    }

    // Debug için
    [ContextMenu("Debug - Pool Stats")]
    private void DebugPoolStats()
    {
        Debug.Log(GetPoolStats());
    }

    [ContextMenu("Debug - Return All Cards")]
    private void DebugReturnAllCards()
    {
        ReturnAllCards();
    }

    [ContextMenu("Debug - Prewarm 5 Cards")]
    private void DebugPrewarm()
    {
        PrewarmPool(5);
    }
}
