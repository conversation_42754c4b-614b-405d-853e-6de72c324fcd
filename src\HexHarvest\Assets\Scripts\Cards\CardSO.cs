using UnityEngine;

[CreateAssetMenu(fileName = "New Card", menuName = "HexHarvest/Card", order = 1)]
public class CardSO : ScriptableObject
{
    [Header("Card Information")]
    [SerializeField] private string cardName = "New Card";
    [SerializeField] private Sprite cardSprite;
    [SerializeField] private Season cardSeason = Season.Spring;
    [SerializeField] private GameObject cardPrefab;

    [Header("Card Appearance")]
    [SerializeField] private Color cardFrameColor = Color.white;

    // Properties
    public string CardName
    {
        get { return cardName; }
        set { cardName = value; }
    }

    public Sprite CardSprite
    {
        get { return cardSprite; }
        set { cardSprite = value; }
    }

    public Season CardSeason
    {
        get { return cardSeason; }
        set { cardSeason = value; }
    }

    public GameObject CardPrefab
    {
        get { return cardPrefab; }
        set { cardPrefab = value; }
    }

    public Color CardFrameColor
    {
        get { return cardFrameColor; }
        set { cardFrameColor = value; }
    }

    /// <summary>
    /// Kartın mevsiminin Türkçe ismini döndürür
    /// </summary>
    /// <returns>Mevsim adı</returns>
    public string GetSeasonString()
    {
        return Seasons.GetSeasonString(cardSeason);
    }

    /// <summary>
    /// Kartın mevsimine göre renk döndürür
    /// </summary>
    /// <returns>Mevsim rengi</returns>
    public Color GetSeasonColor()
    {
        return Seasons.GetSeasonColor(cardSeason);
    }

    /// <summary>
    /// Kartın çerçeve rengini hex string olarak döndürür
    /// </summary>
    /// <returns>Hex renk kodu (#RRGGBB formatında)</returns>
    public string GetFrameColorHex()
    {
        return "#" + ColorUtility.ToHtmlStringRGB(cardFrameColor);
    }

    /// <summary>
    /// Kartın çerçeve rengini alpha ile birlikte hex string olarak döndürür
    /// </summary>
    /// <returns>Hex renk kodu (#RRGGBBAA formatında)</returns>
    public string GetFrameColorHexWithAlpha()
    {
        return "#" + ColorUtility.ToHtmlStringRGBA(cardFrameColor);
    }

    /// <summary>
    /// Hex string'den renk ayarlar
    /// </summary>
    /// <param name="hexColor">Hex renk kodu (#RRGGBB veya #RRGGBBAA)</param>
    /// <returns>Başarılı olup olmadığı</returns>
    public bool SetFrameColorFromHex(string hexColor)
    {
        Color newColor;
        if (ColorUtility.TryParseHtmlString(hexColor, out newColor))
        {
            cardFrameColor = newColor;
            return true;
        }
        return false;
    }
}
