using UnityEngine;

/// <summary>
/// EventBus sistemini test etmek ve debug etmek için kullanılan component
/// </summary>
public class EventBusDebugger : MonoBehaviour
{
    [Header("Debug Settings")]
    [SerializeField] private bool enableDebugMode = true;
    [SerializeField] private bool logAllEvents = false;
    
    [Header("Test Buttons")]
    [SerializeField] private bool testSeasonChange = false;
    [SerializeField] private bool testReadyButton = false;
    [SerializeField] private bool testCardClick = false;
    
    private void Start()
    {
        // EventBus debug modunu ayarla
        EventBus.SetDebugMode(enableDebugMode);
        
        if (logAllEvents)
        {
            // Tüm eventleri dinle
            EventBus.Subscribe<SeasonChangedEvent>(OnSeasonChanged);
            EventBus.Subscribe<SeasonTransitionStateChangedEvent>(OnSeasonTransitionStateChanged);
            EventBus.Subscribe<ReadyButtonClickedEvent>(OnReadyButtonClicked);
            EventBus.Subscribe<CardClickedEvent>(OnCardClicked);
            EventBus.Subscribe<HexStateChangedEvent>(OnHexStateChanged);
            EventBus.Subscribe<CardPoolStateChangedEvent>(OnCardPoolStateChanged);
            EventBus.Subscribe<GameStartedEvent>(OnGameStarted);
            EventBus.Subscribe<GameStoppedEvent>(OnGameStopped);
        }
    }
    
    private void OnDestroy()
    {
        if (logAllEvents)
        {
            // Event listener'larını temizle
            EventBus.Unsubscribe<SeasonChangedEvent>(OnSeasonChanged);
            EventBus.Unsubscribe<SeasonTransitionStateChangedEvent>(OnSeasonTransitionStateChanged);
            EventBus.Unsubscribe<ReadyButtonClickedEvent>(OnReadyButtonClicked);
            EventBus.Unsubscribe<CardClickedEvent>(OnCardClicked);
            EventBus.Unsubscribe<HexStateChangedEvent>(OnHexStateChanged);
            EventBus.Unsubscribe<CardPoolStateChangedEvent>(OnCardPoolStateChanged);
            EventBus.Unsubscribe<GameStartedEvent>(OnGameStarted);
            EventBus.Unsubscribe<GameStoppedEvent>(OnGameStopped);
        }
    }
    
    private void Update()
    {
        // Test butonları
        if (testSeasonChange)
        {
            testSeasonChange = false;
            TestSeasonChange();
        }
        
        if (testReadyButton)
        {
            testReadyButton = false;
            TestReadyButton();
        }
        
        if (testCardClick)
        {
            testCardClick = false;
            TestCardClick();
        }
    }
    
    // Event Handler'lar
    private void OnSeasonChanged(SeasonChangedEvent eventData)
    {
        Debug.Log($"[EventBusDebugger] Season Changed: {eventData.PreviousSeason} → {eventData.NewSeason}");
    }
    
    private void OnSeasonTransitionStateChanged(SeasonTransitionStateChangedEvent eventData)
    {
        Debug.Log($"[EventBusDebugger] Season Transition: {eventData.IsTransitioning} (Duration: {eventData.TransitionDuration}s)");
    }
    
    private void OnReadyButtonClicked(ReadyButtonClickedEvent eventData)
    {
        Debug.Log("[EventBusDebugger] Ready Button Clicked");
    }
    
    private void OnCardClicked(CardClickedEvent eventData)
    {
        Debug.Log($"[EventBusDebugger] Card Clicked: {eventData.Card.CardName} at {eventData.ClickPosition}");
    }
    
    private void OnHexStateChanged(HexStateChangedEvent eventData)
    {
        Debug.Log($"[EventBusDebugger] Hex State Changed: {eventData.PreviousState} → {eventData.NewState}");
    }
    
    private void OnCardPoolStateChanged(CardPoolStateChangedEvent eventData)
    {
        Debug.Log($"[EventBusDebugger] Card Pool State: Available={eventData.AvailableCount}, Used={eventData.UsedCount}, Total={eventData.TotalCount}");
    }
    
    private void OnGameStarted(GameStartedEvent eventData)
    {
        Debug.Log($"[EventBusDebugger] Game Started with season: {eventData.InitialSeason}");
    }
    
    private void OnGameStopped(GameStoppedEvent eventData)
    {
        Debug.Log("[EventBusDebugger] Game Stopped");
    }
    
    // Test Metodları
    private void TestSeasonChange()
    {
        Season testSeason = Season.Summer;
        EventBus.Publish(new SeasonChangedEvent(testSeason, Season.Spring));
        Debug.Log("[EventBusDebugger] Test Season Change event gönderildi");
    }
    
    private void TestReadyButton()
    {
        EventBus.Publish(new ReadyButtonClickedEvent());
        Debug.Log("[EventBusDebugger] Test Ready Button event gönderildi");
    }
    
    private void TestCardClick()
    {
        // Test için dummy card oluştur
        if (Resources.Load<CardSO>("Cards/TestCard") != null)
        {
            CardSO testCard = Resources.Load<CardSO>("Cards/TestCard");
            EventBus.Publish(new CardClickedEvent(testCard, Vector3.zero));
            Debug.Log("[EventBusDebugger] Test Card Click event gönderildi");
        }
        else
        {
            Debug.LogWarning("[EventBusDebugger] Test card bulunamadı!");
        }
    }
    
    // Inspector'da gösterilecek bilgiler
    [ContextMenu("Show EventBus Stats")]
    private void ShowEventBusStats()
    {
        Debug.Log(EventBus.GetDetailedStats());
    }
    
    [ContextMenu("Clear All Events")]
    private void ClearAllEvents()
    {
        EventBus.Clear();
        Debug.Log("[EventBusDebugger] Tüm event listener'lar temizlendi");
    }
}
