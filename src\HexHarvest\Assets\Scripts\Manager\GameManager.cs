using UnityEngine;
using System.Collections;

public class GameManager : MonoBehaviour
{
    [Header("Game State")]
    [SerializeField] private Season currentSeason = Season.Spring;

    [Header("Season Transition")]
    [SerializeField] private float seasonTransitionDuration = 10f;
    [SerializeField] private bool isTransitioning = false;

    [Header("Debug")]
    [SerializeField] private bool enableEventBusDebug = false;

    // Singleton pattern
    public static GameManager Instance { get; private set; }

    // Properties
    public Season CurrentSeason
    {
        get { return currentSeason; }
        private set
        {
            if (currentSeason != value)
            {
                Season previousSeason = currentSeason;
                currentSeason = value;

                // EventBus ile season değişikliğini bildir
                EventBus.Publish(new SeasonChangedEvent(currentSeason, previousSeason));
            }
        }
    }

    public bool IsTransitioning
    {
        get { return isTransitioning; }
        private set
        {
            if (isTransitioning != value)
            {
                isTransitioning = value;

                // EventBus ile transition durumu değişikliğini bildir
                EventBus.Publish(new SeasonTransitionStateChangedEvent(isTransitioning, seasonTransitionDuration));
            }
        }
    }

    public float SeasonTransitionDuration
    {
        get { return seasonTransitionDuration; }
    }

    private void Awake()
    {
        // Singleton setup
        if (Instance == null)
        {
            Instance = this;

            // EventBus debug modunu ayarla
            EventBus.SetDebugMode(enableEventBusDebug);

            // Ready button event'ini dinle
            EventBus.Subscribe<ReadyButtonClickedEvent>(OnReadyButtonClicked);
        }
        else
        {
            Destroy(gameObject);
        }
    }

    private void Start()
    {
        // Oyun başladığını bildir
        EventBus.Publish(new GameStartedEvent(currentSeason));

        // İlk mevsimi ayarla
        EventBus.Publish(new SeasonChangedEvent(currentSeason, currentSeason));
    }

    private void OnDestroy()
    {
        // Event listener'ları temizle
        if (Instance == this)
        {
            EventBus.Unsubscribe<ReadyButtonClickedEvent>(OnReadyButtonClicked);
            EventBus.Publish(new GameStoppedEvent());
        }
    }

    /// <summary>
    /// Ready button event'ini işler
    /// </summary>
    /// <param name="eventData">Event verisi</param>
    private void OnReadyButtonClicked(ReadyButtonClickedEvent eventData)
    {
        NextSeason();
    }

    /// <summary>
    /// Sonraki mevsime geçer (animasyonlu)
    /// </summary>
    public void NextSeason()
    {
        if (!isTransitioning)
        {
            StartCoroutine(SeasonTransitionCoroutine());
        }
        else
        {
            Debug.Log("Mevsim geçişi zaten devam ediyor!");
        }
    }

    /// <summary>
    /// Mevsim geçiş coroutine'i
    /// </summary>
    /// <returns></returns>
    private IEnumerator SeasonTransitionCoroutine()
    {
        IsTransitioning = true;
        Season nextSeason = Seasons.GetNextSeason(currentSeason);

        Debug.Log($"Mevsim geçişi başladı: {Seasons.GetSeasonString(currentSeason)} → {Seasons.GetSeasonString(nextSeason)}");

        // 10 saniye bekle
        yield return new WaitForSeconds(seasonTransitionDuration);

        // Mevsimi değiştir
        CurrentSeason = nextSeason;
        IsTransitioning = false;

        Debug.Log($"Mevsim geçişi tamamlandı: {Seasons.GetSeasonString(currentSeason)}");
    }

    /// <summary>
    /// Belirtilen mevsime geçer
    /// </summary>
    /// <param name="newSeason">Yeni mevsim</param>
    public void SetSeason(Season newSeason)
    {
        CurrentSeason = newSeason;
        Debug.Log($"Mevsim ayarlandı: {Seasons.GetSeasonString(currentSeason)}");
    }

    /// <summary>
    /// Mevcut mevsimin string halini döndürür
    /// </summary>
    /// <returns>Mevsim adı</returns>
    public string GetCurrentSeasonString()
    {
        return Seasons.GetSeasonString(currentSeason);
    }

    /// <summary>
    /// Mevcut mevsimin rengini döndürür
    /// </summary>
    /// <returns>Mevsim rengi</returns>
    public Color GetCurrentSeasonColor()
    {
        return Seasons.GetSeasonColor(currentSeason);
    }
}
