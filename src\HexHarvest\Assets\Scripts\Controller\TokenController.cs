using System.Collections.Generic;
using UnityEngine;

public class CardController : MonoBeh<PERSON><PERSON>
{
    [Header("Card Settings")]
    [SerializeField] private Transform cardContainer; // Kullanılan kartların yerleştirileceği container
    [SerializeField] private string cardsResourcePath = "Cards";
    [SerializeField] private int maxVisibleCards = 5;

    [Header("Debug")]
    [SerializeField] private bool showDebugInfo = false;

    // Card data
    private List<CardSO> allCards = new List<CardSO>();
    private List<CardSO> currentSeasonCards = new List<CardSO>();
    private List<CardUI> displayedCards = new List<CardUI>();

    // Properties
    public int TotalCardsCount => allCards.Count;
    public int CurrentSeasonCardsCount => currentSeasonCards.Count;
    public int DisplayedCardsCount => displayedCards.Count;

    public CardPool pool;
    private void Start()
    {
        // Card container'ı oluştur (eğer atanmamışsa)
        if (cardContainer == null)
        {
            GameObject containerObject = new GameObject("ActiveCards_Container");
            containerObject.transform.SetParent(transform);
            cardContainer = containerObject.transform;
        }

        LoadAllCards();

        // EventBus event'lerini dinle (şimdilik yorum satırı)
        // EventBus.Subscribe<SeasonChangedEvent>(OnSeasonChanged);
        // EventBus.Subscribe<SeasonTransitionStateChangedEvent>(OnSeasonTransitionStateChanged);
        // EventBus.Subscribe<GameStartedEvent>(OnGameStarted);

        // Geçici olarak GameManager event'lerini dinle
        if (GameManager.Instance != null)
        {
            UpdateCurrentSeasonCards(GameManager.Instance.CurrentSeason);
            RefreshDisplayedCards();
        }
    }

    private void OnDestroy()
    {
        // EventBus listener'larını temizle (şimdilik yorum satırı)
        // EventBus.Unsubscribe<SeasonChangedEvent>(OnSeasonChanged);
        // EventBus.Unsubscribe<SeasonTransitionStateChangedEvent>(OnSeasonTransitionStateChanged);
        // EventBus.Unsubscribe<GameStartedEvent>(OnGameStarted);
    }

    /// <summary>
    /// Resources klasöründen tüm kartları yükler
    /// </summary>
    private void LoadAllCards()
    {
        allCards.Clear();

        // Resources'dan tüm CardSO'ları yükle
        CardSO[] loadedCards = Resources.LoadAll<CardSO>(cardsResourcePath);

        if (loadedCards.Length == 0)
        {
            Debug.LogWarning($"TokenController: '{cardsResourcePath}' klasöründe kart bulunamadı!");
            return;
        }

        allCards.AddRange(loadedCards);

        if (showDebugInfo)
        {
            Debug.Log($"TokenController: {allCards.Count} kart yüklendi");
            foreach (CardSO card in allCards)
            {
                Debug.Log($"- {card.CardName} ({card.GetSeasonString()})");
            }
        }
    }

    /*
    /// <summary>
    /// Oyun başladığında çağrılır
    /// </summary>
    /// <param name="eventData">Event verisi</param>
    private void OnGameStarted(GameStartedEvent eventData)
    {
        // İlk mevsimi ayarla
        UpdateCurrentSeasonCards(eventData.InitialSeason);
        RefreshDisplayedCards();

        if (showDebugInfo)
        {
            Debug.Log($"TokenController: Oyun başladı - {Seasons.GetSeasonString(eventData.InitialSeason)}. " +
                     $"Bu mevsime ait {currentSeasonCards.Count} kart bulundu.");
        }
    }

    /// <summary>
    /// Mevsim değiştiğinde çağrılır
    /// </summary>
    /// <param name="eventData">Event verisi</param>
    private void OnSeasonChanged(SeasonChangedEvent eventData)
    {
        UpdateCurrentSeasonCards(eventData.NewSeason);
        RefreshDisplayedCards();

        if (showDebugInfo)
        {
            Debug.Log($"TokenController: Mevsim değişti - {Seasons.GetSeasonString(eventData.NewSeason)}. " +
                     $"Bu mevsime ait {currentSeasonCards.Count} kart bulundu.");
        }
    }

    /// <summary>
    /// Mevsim geçiş durumu değiştiğinde çağrılır
    /// </summary>
    /// <param name="eventData">Event verisi</param>
    private void OnSeasonTransitionStateChanged(SeasonTransitionStateChangedEvent eventData)
    {
        bool isTransitioning = eventData.IsTransitioning;

        if (isTransitioning)
        {
            // Geri sayım başladığında kartları interactable yapma
            SetAllCardsInteractable(false);

            if (showDebugInfo)
            {
                Debug.Log("TokenController: Mevsim geçişi başladı - Kartlar interactable değil");
            }
        }
        else
        {
            // Geri sayım bittiğinde kartları interactable yap, sonra poola döndür
            SetAllCardsInteractable(true);

            if (showDebugInfo)
            {
                Debug.Log("TokenController: Mevsim geçişi bitti - Kartlar interactable yapıldı");
            }
        }
    }
    */

    /// <summary>
    /// Mevcut mevsime ait kartları filtreler
    /// </summary>
    /// <param name="season">Mevsim</param>
    private void UpdateCurrentSeasonCards(Season season)
    {
        currentSeasonCards.Clear();

        foreach (CardSO card in allCards)
        {
            if (card.CardSeason == season)
            {
                currentSeasonCards.Add(card);
            }
        }
    }

    /// <summary>
    /// Görüntülenen kartları yeniler
    /// </summary>
    private void RefreshDisplayedCards()
    {
        // Mevcut kartları pool'a döndür
        ReturnAllDisplayedCards();

        // Yeni kartları göster
        DisplayCards();
    }

    /// <summary>
    /// Kartları görüntüler
    /// </summary>
    private void DisplayCards()
    {
        if (pool == null)
        {
            Debug.LogError("TokenController: CardPool bulunamadı!");
            return;
        }

        int cardsToShow = Mathf.Min(maxVisibleCards, currentSeasonCards.Count);

        for (int i = 0; i < cardsToShow; i++)
        {
            // Kartı pool'dan al ve CardController'ın container'ına yerleştir
            CardUI cardUI = pool.GetCard(cardContainer);
            if (cardUI != null)
            {
                cardUI.SetCard(currentSeasonCards[i]);
                displayedCards.Add(cardUI);
            }
        }

        if (showDebugInfo)
        {
            Debug.Log($"TokenController: {cardsToShow} kart görüntülendi ve cardContainer'a yerleştirildi");
        }
    }

    /// <summary>
    /// Tüm görüntülenen kartları pool'a döndürür
    /// </summary>
    private void ReturnAllDisplayedCards()
    {
        if (pool == null) return;

        foreach (CardUI cardUI in displayedCards)
        {
            if (cardUI != null)
            {
                pool.ReturnCard(cardUI);
            }
        }

        displayedCards.Clear();
    }

    /// <summary>
    /// Tüm görüntülenen kartların interactable durumunu ayarlar
    /// </summary>
    /// <param name="interactable">Interactable mı</param>
    private void SetAllCardsInteractable(bool interactable)
    {
        foreach (CardUI cardUI in displayedCards)
        {
            if (cardUI != null)
            {
                cardUI.SetInteractable(interactable);
            }
        }

        if (showDebugInfo)
        {
            Debug.Log($"TokenController: {displayedCards.Count} kartın interactable durumu {interactable} olarak ayarlandı");
        }
    }

    /// <summary>
    /// Kartları yeniden yükler
    /// </summary>
    public void ReloadCards()
    {
        LoadAllCards();

        // GameManager bağımlılığı kaldırıldı
        // Mevcut mevsim bilgisi event sistemi üzerinden gelecek
        if (showDebugInfo)
        {
            Debug.Log("TokenController: Kartlar yeniden yüklendi. Event sistemi ile güncelleme bekleniyor.");
        }
    }

    /// <summary>
    /// Maksimum görüntülenecek kart sayısını ayarlar
    /// </summary>
    /// <param name="maxCards">Maksimum kart sayısı</param>
    public void SetMaxVisibleCards(int maxCards)
    {
        maxVisibleCards = Mathf.Max(1, maxCards);
        RefreshDisplayedCards();
    }

    /// <summary>
    /// Controller istatistiklerini döndürür
    /// </summary>
    /// <returns>İstatistik bilgileri</returns>
    public string GetStats()
    {
        return $"TokenController - Toplam Kart: {TotalCardsCount}, " +
               $"Mevcut Mevsim: {CurrentSeasonCardsCount}, " +
               $"Görüntülenen: {DisplayedCardsCount}";
    }

    // Debug metodları
    [ContextMenu("Debug - Reload Cards")]
    private void DebugReloadCards()
    {
        ReloadCards();
    }

    [ContextMenu("Debug - Show Stats")]
    private void DebugShowStats()
    {
        Debug.Log(GetStats());
        if (pool!= null)
        {
            Debug.Log(pool.GetPoolStats());
        }
    }

    [ContextMenu("Debug - Refresh Display")]
    private void DebugRefreshDisplay()
    {
        RefreshDisplayedCards();
    }
}
